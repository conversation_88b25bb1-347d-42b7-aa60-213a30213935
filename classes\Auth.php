<?php
/**
 * Authentication Class
 * Handles user authentication, JWT tokens, and session management
 */

require_once __DIR__ . '/../vendor/autoload.php';
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class Auth {
    private $db;
    private $secret_key;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->secret_key = JWT_SECRET;
    }
    
    /**
     * Authenticate user login
     */
    public function login($username, $password) {
        try {
            $query = "SELECT id, username, email, password, first_name, last_name, role, is_active 
                     FROM users WHERE (username = :username OR email = :username) AND is_active = 1";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':username', $username);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (password_verify($password, $user['password'])) {
                    // Update last login
                    $this->updateLastLogin($user['id']);
                    
                    // Generate JWT token
                    $token = $this->generateJWT($user);
                    
                    return [
                        'success' => true,
                        'token' => $token,
                        'user' => [
                            'id' => $user['id'],
                            'username' => $user['username'],
                            'email' => $user['email'],
                            'first_name' => $user['first_name'],
                            'last_name' => $user['last_name'],
                            'role' => $user['role']
                        ]
                    ];
                }
            }
            
            return ['success' => false, 'message' => 'Invalid credentials'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Login failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Generate JWT token
     */
    private function generateJWT($user) {
        $issued_at = time();
        $expiration_time = $issued_at + JWT_EXPIRATION;
        
        $payload = [
            'iss' => SITE_URL,
            'aud' => SITE_URL,
            'iat' => $issued_at,
            'exp' => $expiration_time,
            'data' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'role' => $user['role']
            ]
        ];
        
        return JWT::encode($payload, $this->secret_key, JWT_ALGORITHM);
    }
    
    /**
     * Verify JWT token
     */
    public function verifyToken($token) {
        try {
            $decoded = JWT::decode($token, new Key($this->secret_key, JWT_ALGORITHM));
            return [
                'success' => true,
                'data' => $decoded->data
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Invalid token: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get current user from token
     */
    public function getCurrentUser($token) {
        $verification = $this->verifyToken($token);
        
        if ($verification['success']) {
            $user_id = $verification['data']->id;
            
            $query = "SELECT id, username, email, first_name, last_name, role, avatar 
                     FROM users WHERE id = :id AND is_active = 1";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $user_id);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                return [
                    'success' => true,
                    'user' => $stmt->fetch(PDO::FETCH_ASSOC)
                ];
            }
        }
        
        return ['success' => false, 'message' => 'User not found'];
    }
    
    /**
     * Check if user has required role
     */
    public function hasRole($token, $required_roles) {
        $verification = $this->verifyToken($token);
        
        if ($verification['success']) {
            $user_role = $verification['data']->role;
            
            if (is_string($required_roles)) {
                $required_roles = [$required_roles];
            }
            
            return in_array($user_role, $required_roles);
        }
        
        return false;
    }
    
    /**
     * Register new user (admin only)
     */
    public function register($data, $created_by_token) {
        try {
            // Verify admin permissions
            if (!$this->hasRole($created_by_token, ['admin'])) {
                return ['success' => false, 'message' => 'Insufficient permissions'];
            }
            
            // Validate required fields
            $required_fields = ['username', 'email', 'password', 'first_name', 'last_name'];
            foreach ($required_fields as $field) {
                if (empty($data[$field])) {
                    return ['success' => false, 'message' => "Field {$field} is required"];
                }
            }
            
            // Check if username or email already exists
            $query = "SELECT id FROM users WHERE username = :username OR email = :email";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':username', $data['username']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                return ['success' => false, 'message' => 'Username or email already exists'];
            }
            
            // Hash password
            $hashed_password = password_hash($data['password'], PASSWORD_DEFAULT);
            
            // Insert new user
            $query = "INSERT INTO users (username, email, password, first_name, last_name, role, created_by) 
                     VALUES (:username, :email, :password, :first_name, :last_name, :role, :created_by)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':username', $data['username']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':first_name', $data['first_name']);
            $stmt->bindParam(':last_name', $data['last_name']);
            $stmt->bindParam(':role', $data['role'] ?? 'editor');
            
            $creator = $this->getCurrentUser($created_by_token);
            $created_by = $creator['success'] ? $creator['user']['id'] : null;
            $stmt->bindParam(':created_by', $created_by);
            
            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'User created successfully',
                    'user_id' => $this->db->lastInsertId()
                ];
            }
            
            return ['success' => false, 'message' => 'Failed to create user'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Registration failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update last login timestamp
     */
    private function updateLastLogin($user_id) {
        $query = "UPDATE users SET last_login = NOW() WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();
    }
    
    /**
     * Change password
     */
    public function changePassword($token, $current_password, $new_password) {
        try {
            $user_result = $this->getCurrentUser($token);
            if (!$user_result['success']) {
                return ['success' => false, 'message' => 'Invalid user'];
            }
            
            $user_id = $user_result['user']['id'];
            
            // Verify current password
            $query = "SELECT password FROM users WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $user_id);
            $stmt->execute();
            
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!password_verify($current_password, $user['password'])) {
                return ['success' => false, 'message' => 'Current password is incorrect'];
            }
            
            // Update password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            
            $query = "UPDATE users SET password = :password WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':id', $user_id);
            
            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'Password updated successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to update password'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Password change failed: ' . $e->getMessage()];
        }
    }
}
?>
