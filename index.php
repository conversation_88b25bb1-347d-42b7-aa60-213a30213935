<?php
/**
 * Homepage - Flori Construction Ltd
 */

require_once 'config/config.php';
require_once 'classes/Project.php';
require_once 'classes/Service.php';

// Get featured projects and services
$projectClass = new Project();
$serviceClass = new Service();

$featuredProjects = $projectClass->getFeaturedProjects(6);
$featuredServices = $serviceClass->getFeaturedServices(5);

$pageTitle = 'Professional Construction Services in London';
$pageDescription = 'Flori Construction Ltd - Expert construction services including civil engineering, groundworks, RC frames, basements, and hard landscaping across London.';

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-slider">
        <div class="hero-slide active" style="background-image: url('assets/images/hero/construction-site-1.jpg');">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Professional Construction Services</h1>
                    <p class="hero-subtitle">Quality construction solutions for residential and commercial projects across London</p>
                    <div class="hero-buttons">
                        <a href="/projects" class="btn btn-primary">View Our Projects</a>
                        <a href="/contact" class="btn btn-outline">Get Quote</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="hero-slide" style="background-image: url('assets/images/hero/construction-team.jpg');">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Expert Construction Team</h1>
                    <p class="hero-subtitle">Experienced professionals delivering exceptional results with passion and precision</p>
                    <div class="hero-buttons">
                        <a href="/about" class="btn btn-primary">About Us</a>
                        <a href="/services" class="btn btn-outline">Our Services</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="hero-slide" style="background-image: url('assets/images/hero/modern-building.jpg');">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Modern Construction Solutions</h1>
                    <p class="hero-subtitle">Innovative techniques and cutting-edge technology for superior construction quality</p>
                    <div class="hero-buttons">
                        <a href="/services" class="btn btn-primary">Our Services</a>
                        <a href="/contact" class="btn btn-outline">Contact Us</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Hero Navigation -->
    <div class="hero-nav">
        <button class="hero-prev" onclick="changeSlide(-1)">&#8249;</button>
        <button class="hero-next" onclick="changeSlide(1)">&#8250;</button>
    </div>
    
    <!-- Hero Indicators -->
    <div class="hero-indicators">
        <span class="indicator active" onclick="currentSlide(1)"></span>
        <span class="indicator" onclick="currentSlide(2)"></span>
        <span class="indicator" onclick="currentSlide(3)"></span>
    </div>
</section>

<!-- About Section -->
<section class="about-section py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="about-content">
                    <h2 class="section-title">About Our Company</h2>
                    <p class="lead">Flori Construction Ltd began its journey in the heart of London, founded on the principles of quality, professionalism, and an unwavering dedication to customer satisfaction.</p>
                    <p>From our humble beginnings, we have grown exponentially, establishing ourselves as a premier construction company renowned for our exceptional services in Civil Engineering, Professional Groundworks, RC Frames Construction, Basement Construction, and Hard Landscaping.</p>
                    
                    <div class="about-features">
                        <div class="feature-item">
                            <i class="fas fa-award"></i>
                            <div>
                                <h4>Quality Assurance</h4>
                                <p>Committed to delivering the highest standards in every project</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <div>
                                <h4>Expert Team</h4>
                                <p>Experienced professionals with years of industry expertise</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <div>
                                <h4>On-Time Delivery</h4>
                                <p>Projects completed on schedule without compromising quality</p>
                            </div>
                        </div>
                    </div>
                    
                    <a href="/about" class="btn btn-primary">Learn More About Us</a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="about-image">
                    <img src="assets/images/about/team-photo.jpg" alt="Flori Construction Team" class="img-fluid rounded">
                    <div class="experience-badge">
                        <span class="number">15+</span>
                        <span class="text">Years Experience</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="services-section py-5 bg-light">
    <div class="container">
        <div class="section-header text-center">
            <h2 class="section-title">Services We Provide</h2>
            <p class="section-subtitle">We are committed to delivering high-quality services in these areas and more. Our goal is to provide reliable and efficient solutions that meet the unique needs of each project.</p>
        </div>
        
        <div class="row">
            <?php if ($featuredServices['success'] && !empty($featuredServices['data'])): ?>
                <?php foreach ($featuredServices['data'] as $service): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="<?php echo htmlspecialchars($service['icon'] ?: 'fas fa-tools'); ?>"></i>
                        </div>
                        <div class="service-content">
                            <h3 class="service-title"><?php echo htmlspecialchars($service['title']); ?></h3>
                            <p class="service-description"><?php echo htmlspecialchars($service['short_description']); ?></p>
                            <a href="/services/<?php echo htmlspecialchars($service['slug']); ?>" class="service-link">Learn More <i class="fas fa-arrow-right"></i></a>
                        </div>
                        <?php if ($service['featured_image']): ?>
                        <div class="service-image">
                            <img src="<?php echo htmlspecialchars($service['featured_image']); ?>" alt="<?php echo htmlspecialchars($service['title']); ?>" class="img-fluid">
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <p class="text-center">No services available at the moment.</p>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="/services" class="btn btn-primary">View All Services</a>
        </div>
    </div>
</section>

<!-- Projects Section -->
<section class="projects-section py-5">
    <div class="container">
        <div class="section-header text-center">
            <h2 class="section-title">Latest Projects</h2>
            <p class="section-subtitle">Every Project is Unique and Custom Made</p>
            <p>We are proud to showcase a selection of our completed projects that demonstrate our expertise and commitment to delivering high-quality construction solutions.</p>
        </div>
        
        <div class="row">
            <?php if ($featuredProjects['success'] && !empty($featuredProjects['data'])): ?>
                <?php foreach ($featuredProjects['data'] as $project): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="project-card">
                        <?php if ($project['featured_image']): ?>
                        <div class="project-image">
                            <img src="<?php echo htmlspecialchars($project['featured_image']); ?>" alt="<?php echo htmlspecialchars($project['title']); ?>" class="img-fluid">
                            <div class="project-overlay">
                                <a href="/projects/<?php echo htmlspecialchars($project['slug']); ?>" class="project-link">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="project-content">
                            <div class="project-category"><?php echo ucfirst(str_replace('-', ' ', $project['category'])); ?></div>
                            <h3 class="project-title">
                                <a href="/projects/<?php echo htmlspecialchars($project['slug']); ?>">
                                    <?php echo htmlspecialchars($project['title']); ?>
                                </a>
                            </h3>
                            <p class="project-description"><?php echo htmlspecialchars($project['short_description']); ?></p>
                            <?php if ($project['location']): ?>
                            <div class="project-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <?php echo htmlspecialchars($project['location']); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <p class="text-center">No projects available at the moment.</p>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="/projects" class="btn btn-primary">View All Projects</a>
        </div>
    </div>
</section>

<!-- Contact CTA Section -->
<section class="contact-cta py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2 class="cta-title">Ready to Start Your Project?</h2>
                <p class="cta-subtitle">Get in touch with our expert team for a free consultation and quote</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="/contact" class="btn btn-light btn-lg">Get Free Quote</a>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

<script>
// Hero slider functionality
let currentSlideIndex = 0;
const slides = document.querySelectorAll('.hero-slide');
const indicators = document.querySelectorAll('.indicator');

function showSlide(index) {
    slides.forEach(slide => slide.classList.remove('active'));
    indicators.forEach(indicator => indicator.classList.remove('active'));
    
    slides[index].classList.add('active');
    indicators[index].classList.add('active');
}

function changeSlide(direction) {
    currentSlideIndex += direction;
    if (currentSlideIndex >= slides.length) currentSlideIndex = 0;
    if (currentSlideIndex < 0) currentSlideIndex = slides.length - 1;
    showSlide(currentSlideIndex);
}

function currentSlide(index) {
    currentSlideIndex = index - 1;
    showSlide(currentSlideIndex);
}

// Auto-advance slides
setInterval(() => {
    changeSlide(1);
}, 5000);
</script>
