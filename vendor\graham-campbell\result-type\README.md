Result Type
===========

Result Type was created by, and is maintained by [<PERSON>](https://github.com/<PERSON><PERSON>), and is an implementation of the result type. Feel free to check out the [change log](CHANGELOG.md), [releases](https://github.com/GrahamCampbell/Result-Type/releases), [security policy](https://github.com/GrahamCampbell/Result-Type/security/policy), [license](LICENSE), [code of conduct](.github/CODE_OF_CONDUCT.md), and [contribution guidelines](.github/CONTRIBUTING.md).

![Banner](https://user-images.githubusercontent.com/2829600/77233209-f86aae80-6b9d-11ea-9cef-81501c9e858d.png)

<p align="center">
<a href="https://github.com/GrahamCampbell/Result-Type/actions?query=workflow%3ATests"><img src="https://img.shields.io/github/actions/workflow/status/GrahamCampbell/Result-Type/tests.yml?label=Tests&style=flat-square" alt="Build Status"></img></a>
<a href="https://github.styleci.io/repos/249026522"><img src="https://github.styleci.io/repos/249026522/shield" alt="StyleCI Status"></img></a>
<a href="LICENSE"><img src="https://img.shields.io/badge/license-MIT-brightgreen?style=flat-square" alt="Software License"></img></a>
<a href="https://packagist.org/packages/graham-campbell/result-type"><img src="https://img.shields.io/packagist/dt/graham-campbell/result-type?style=flat-square" alt="Packagist Downloads"></img></a>
<a href="https://github.com/GrahamCampbell/Result-Type/releases"><img src="https://img.shields.io/github/release/GrahamCampbell/Result-Type?style=flat-square" alt="Latest Version"></img></a>
</p>


## Installation

This version requires [PHP](https://www.php.net/) 7.2.5-8.4.

To get the latest version, simply require the project using [Composer](https://getcomposer.org/):

```bash
$ composer require "graham-campbell/result-type:^1.1"
```


## Security

If you discover a security vulnerability within this package, please send an <NAME_EMAIL>. All security vulnerabilities will be promptly addressed. You may view our full security policy [here](https://github.com/GrahamCampbell/Result-Type/security/policy).


## License

Result Type is licensed under [The MIT License (MIT)](LICENSE).


## For Enterprise

Available as part of the Tidelift Subscription

The maintainers of `graham-campbell/result-type` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/packagist-graham-campbell-result-type?utm_source=packagist-graham-campbell-result-type&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)
