# This is a comment
CFOO=bar
#CBAR=baz
#CZOO=goo # a comment on a commented row
CSPACED="with spaces" # this is a comment
CQUOTES="a value with a # character" # this is a comment
CQUOTESWITHQUOTE="a value with a # character & a quote \" character inside quotes" # " this is a comment
EMPTY= # comment with empty variable
EMPTY2=# comment with empty variable
FOOO=foo# comment with no space
BOOLEAN=yes # (yes, no)

CNULL=

## this is a comment ##
