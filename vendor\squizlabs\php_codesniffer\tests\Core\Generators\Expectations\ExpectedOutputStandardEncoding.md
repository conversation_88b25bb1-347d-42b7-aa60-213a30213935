# GeneratorTest Coding Standard

## Standard Element, handling of HTML tags

The use of *tags* in standard descriptions is allowed and their handling should be *safeguarded*.  
Other tags, like &lt;a href=&quot;example.com&quot;&gt;link&lt;/a&gt;, &lt;b&gt;bold&lt;/bold&gt;, &lt;script&gt;&lt;/script&gt; are not allowed and will be encoded for display when the HTML or Markdown report is used.

Documentation generated on *REDACTED* by [PHP_CodeSniffer *VERSION*](https://github.com/PHPCSStandards/PHP_CodeSniffer)
