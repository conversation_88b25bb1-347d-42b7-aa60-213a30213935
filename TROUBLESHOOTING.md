# Troubleshooting Guide - Flori Construction Website

## Common Issues and Solutions

### 1. Internal Server Error (500)

**Symptoms:**
- "Internal Server Error" message when accessing the website
- Blank white page
- Apache error in browser

**Solutions:**

#### A. Check PHP Syntax Errors
1. Open `test.php` in browser: `http://localhost/flori-construction-web-app/test.php`
2. Look for any PHP syntax errors or missing files

#### B. Check Apache Error Logs
1. Open XAMPP Control Panel
2. Click "Logs" next to Apache
3. Look for recent errors

#### C. Check File Permissions
1. Ensure all files are readable by the web server
2. Make sure `uploads/` directory exists and is writable

#### D. Try Simple Version First
1. Access `http://localhost/flori-construction-web-app/index_simple.php`
2. This version doesn't require database connection

### 2. Database Connection Errors

**Symptoms:**
- "Database connection failed" messages
- "Table doesn't exist" errors

**Solutions:**

#### A. Setup Database
1. Run the setup script: `http://localhost/flori-construction-web-app/setup.php`
2. This will create the database and tables automatically

#### B. Check MySQL Service
1. Open XAMPP Control Panel
2. Ensure MySQL is running (green "Running" status)
3. If not running, click "Start"

#### C. Verify Database Credentials
1. Open `config/database.php`
2. Check these settings:
   ```php
   private $host = 'localhost';
   private $db_name = 'flori_construction';
   private $username = 'root';
   private $password = '';
   ```

#### D. Test Database Connection
1. Open phpMyAdmin: `http://localhost/phpmyadmin`
2. Check if `flori_construction` database exists
3. Verify tables are created

### 3. Missing Dependencies

**Symptoms:**
- "Class not found" errors
- "require_once" errors

**Solutions:**

#### A. Install Composer Dependencies
```bash
cd C:\xampp\htdocs\flori-construction-web-app
composer install
```

#### B. Check Autoloader
1. Verify `vendor/autoload.php` exists
2. If not, run `composer install` again

### 4. Page Not Found (404)

**Symptoms:**
- 404 errors when accessing pages
- URL rewriting not working

**Solutions:**

#### A. Check .htaccess File
1. Ensure `.htaccess` file exists in root directory
2. Check if Apache mod_rewrite is enabled

#### B. Enable mod_rewrite in XAMPP
1. Open `C:\xampp\apache\conf\httpd.conf`
2. Find line: `#LoadModule rewrite_module modules/mod_rewrite.so`
3. Remove the `#` to uncomment it
4. Restart Apache

#### C. Use Direct File Access
Instead of `/about`, use `/about.php`

### 5. Admin Panel Issues

**Symptoms:**
- Cannot login to admin panel
- "Invalid credentials" errors

**Solutions:**

#### A. Use Default Credentials
- Username: `admin`
- Password: `admin123`

#### B. Reset Admin Password
1. Run setup script again: `http://localhost/flori-construction-web-app/setup.php`
2. This will recreate the default admin user

#### C. Check Database
1. Open phpMyAdmin
2. Go to `flori_construction` database
3. Check `users` table has admin user

### 6. CSS/JavaScript Not Loading

**Symptoms:**
- Website appears unstyled
- No Bootstrap styling
- JavaScript features not working

**Solutions:**

#### A. Check Internet Connection
- Bootstrap and Font Awesome load from CDN
- Ensure internet connection is available

#### B. Check File Paths
1. Verify `assets/css/style.css` exists
2. Check `assets/js/main.js` exists

#### C. Clear Browser Cache
1. Press Ctrl+F5 to hard refresh
2. Or clear browser cache manually

### 7. File Upload Issues

**Symptoms:**
- Cannot upload images
- "Permission denied" errors

**Solutions:**

#### A. Create Upload Directories
```bash
mkdir uploads
mkdir uploads/projects
mkdir uploads/services
mkdir uploads/media
```

#### B. Set Permissions (if on Linux/Mac)
```bash
chmod 755 uploads/
chmod 755 uploads/projects/
chmod 755 uploads/services/
chmod 755 uploads/media/
```

## Quick Diagnostic Steps

### Step 1: Basic Test
1. Access: `http://localhost/flori-construction-web-app/test.php`
2. Check all green checkmarks

### Step 2: Simple Version
1. Access: `http://localhost/flori-construction-web-app/index_simple.php`
2. Should work without database

### Step 3: Database Setup
1. Access: `http://localhost/flori-construction-web-app/setup.php`
2. Follow the setup process

### Step 4: Full Version
1. Access: `http://localhost/flori-construction-web-app/index.php`
2. Should work with database

### Step 5: Admin Panel
1. Access: `http://localhost/flori-construction-web-app/admin/login.php`
2. Login with admin/admin123

## Environment Requirements

### Minimum Requirements
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache with mod_rewrite enabled
- 512MB RAM
- 100MB disk space

### Recommended Setup
- XAMPP 8.0+ (includes PHP 8.0+, MySQL 8.0+, Apache)
- 1GB RAM
- 500MB disk space

## Getting Help

If you're still experiencing issues:

1. **Check the test page**: `http://localhost/flori-construction-web-app/test.php`
2. **Review Apache error logs** in XAMPP Control Panel
3. **Try the simple version** first: `index_simple.php`
4. **Run the setup script**: `setup.php`

## Common File Locations

- **Main website**: `http://localhost/flori-construction-web-app/`
- **Admin panel**: `http://localhost/flori-construction-web-app/admin/`
- **Test page**: `http://localhost/flori-construction-web-app/test.php`
- **Setup script**: `http://localhost/flori-construction-web-app/setup.php`
- **phpMyAdmin**: `http://localhost/phpmyadmin/`

## Contact Information

For additional support:
- Email: <EMAIL>
- Phone: 0208 914 7883
