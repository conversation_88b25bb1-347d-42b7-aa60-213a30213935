# Minimal .htaccess for Flori Construction
# This is a safe, minimal configuration

# Prevent directory browsing
Options -Indexes

# Hide sensitive files
<Files "*.sql">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files ".htaccess">
    Require all denied
</Files>

<Files "composer.*">
    Require all denied
</Files>

# Basic URL rewriting (only if mod_rewrite is available)
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Admin redirect
    RewriteRule ^admin/?$ admin/login.php [L]
</IfModule>
