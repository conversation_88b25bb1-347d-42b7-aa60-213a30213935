<?php
/**
 * API Router
 * Main entry point for API requests
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config/config.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = str_replace('/flori-construction-web-app/api', '', $path);
$path = trim($path, '/');

// Parse path segments
$segments = explode('/', $path);

try {
    // Route requests
    switch ($segments[0]) {
        case 'auth':
            handleAuthRoutes($segments, $method);
            break;
            
        case 'projects':
            handleProjectRoutes($segments, $method);
            break;
            
        case 'services':
            handleServiceRoutes($segments, $method);
            break;
            
        case 'media':
            handleMediaRoutes($segments, $method);
            break;
            
        case 'contact':
            handleContactRoutes($segments, $method);
            break;
            
        case 'admin':
            handleAdminRoutes($segments, $method);
            break;
            
        default:
            jsonResponse(['error' => 'Endpoint not found'], 404);
    }
    
} catch (Exception $e) {
    jsonResponse(['error' => 'Internal server error: ' . $e->getMessage()], 500);
}

/**
 * Handle authentication routes
 */
function handleAuthRoutes($segments, $method) {
    switch ($segments[1] ?? '') {
        case 'login':
            if ($method === 'POST') {
                include __DIR__ . '/auth/login.php';
            } else {
                jsonResponse(['error' => 'Method not allowed'], 405);
            }
            break;
            
        case 'register':
            if ($method === 'POST') {
                include __DIR__ . '/auth/register.php';
            } else {
                jsonResponse(['error' => 'Method not allowed'], 405);
            }
            break;
            
        case 'logout':
            if ($method === 'POST') {
                include __DIR__ . '/auth/logout.php';
            } else {
                jsonResponse(['error' => 'Method not allowed'], 405);
            }
            break;
            
        case 'verify':
            if ($method === 'GET') {
                include __DIR__ . '/auth/verify.php';
            } else {
                jsonResponse(['error' => 'Method not allowed'], 405);
            }
            break;
            
        default:
            jsonResponse(['error' => 'Auth endpoint not found'], 404);
    }
}

/**
 * Handle project routes
 */
function handleProjectRoutes($segments, $method) {
    switch ($method) {
        case 'GET':
            if (isset($segments[1])) {
                // Get single project
                include __DIR__ . '/projects/single.php';
            } else {
                // Get all projects
                include __DIR__ . '/projects/index.php';
            }
            break;
            
        case 'POST':
            include __DIR__ . '/projects/create.php';
            break;
            
        case 'PUT':
            include __DIR__ . '/projects/update.php';
            break;
            
        case 'DELETE':
            include __DIR__ . '/projects/delete.php';
            break;
            
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
}

/**
 * Handle service routes
 */
function handleServiceRoutes($segments, $method) {
    switch ($method) {
        case 'GET':
            if (isset($segments[1])) {
                // Get single service
                include __DIR__ . '/services/single.php';
            } else {
                // Get all services
                include __DIR__ . '/services/index.php';
            }
            break;
            
        case 'POST':
            include __DIR__ . '/services/create.php';
            break;
            
        case 'PUT':
            include __DIR__ . '/services/update.php';
            break;
            
        case 'DELETE':
            include __DIR__ . '/services/delete.php';
            break;
            
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
}

/**
 * Handle media routes
 */
function handleMediaRoutes($segments, $method) {
    switch ($method) {
        case 'GET':
            include __DIR__ . '/media/index.php';
            break;
            
        case 'POST':
            if (isset($segments[1]) && $segments[1] === 'upload') {
                include __DIR__ . '/media/upload.php';
            } else {
                include __DIR__ . '/media/create.php';
            }
            break;
            
        case 'PUT':
            include __DIR__ . '/media/update.php';
            break;
            
        case 'DELETE':
            include __DIR__ . '/media/delete.php';
            break;
            
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
}

/**
 * Handle contact routes
 */
function handleContactRoutes($segments, $method) {
    switch ($segments[1] ?? '') {
        case 'submit':
            if ($method === 'POST') {
                include __DIR__ . '/contact/submit.php';
            } else {
                jsonResponse(['error' => 'Method not allowed'], 405);
            }
            break;
            
        default:
            jsonResponse(['error' => 'Contact endpoint not found'], 404);
    }
}

/**
 * Handle admin routes
 */
function handleAdminRoutes($segments, $method) {
    // Verify admin authentication for all admin routes
    $auth = new Auth();
    $token = getBearerToken();
    
    if (!$token || !$auth->hasRole($token, ['admin', 'manager', 'editor'])) {
        jsonResponse(['error' => 'Unauthorized'], 401);
        return;
    }
    
    switch ($segments[1] ?? '') {
        case 'dashboard':
            if ($method === 'GET') {
                include __DIR__ . '/admin/dashboard.php';
            } else {
                jsonResponse(['error' => 'Method not allowed'], 405);
            }
            break;
            
        case 'projects':
            include __DIR__ . '/admin/projects.php';
            break;
            
        case 'services':
            include __DIR__ . '/admin/services.php';
            break;
            
        case 'media':
            include __DIR__ . '/admin/media.php';
            break;
            
        case 'contacts':
            include __DIR__ . '/admin/contacts.php';
            break;
            
        case 'settings':
            include __DIR__ . '/admin/settings.php';
            break;
            
        default:
            jsonResponse(['error' => 'Admin endpoint not found'], 404);
    }
}

/**
 * Get Bearer Token from Authorization header
 */
function getBearerToken() {
    $headers = getallheaders();
    
    if (isset($headers['Authorization'])) {
        $matches = [];
        if (preg_match('/Bearer\s(\S+)/', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }
    
    return null;
}
?>
