name: Static Analysis

on:
  push:
  pull_request:

jobs:
  phpstan:
    name: PHP<PERSON>tan
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          tools: composer:v2
          coverage: none
        env:
          update: true

      - name: Install Dependencies
        uses: nick-invision/retry@v2
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer update --no-interaction --no-progress

      - name: Install PHPStan
        uses: nick-invision/retry@v2
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer bin phpstan update --no-interaction --no-progress

      - name: Execute PHPStan
        run: vendor/bin/phpstan analyze --no-progress

  psalm:
    name: Psalm
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          tools: composer:v2
          coverage: none
        env:
          update: true

      - name: Install Dependencies
        uses: nick-invision/retry@v2
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer update --no-interaction --no-progress

      - name: Install Psalm
        uses: nick-invision/retry@v2
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer bin psalm update --no-interaction --no-progress

      - name: Execute Psalm
        run: vendor/bin/psalm.phar --no-progress --output-format=github
