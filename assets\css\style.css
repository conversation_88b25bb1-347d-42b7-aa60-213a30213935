/* Flori Construction Ltd - Main Stylesheet */

/* CSS Variables */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #f39c12;
    --accent-color: #e74c3c;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --white-color: #ffffff;
    --gray-color: #7f8c8d;
    --light-gray: #bdc3c7;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    
    --font-primary: 'Inter', sans-serif;
    --font-heading: 'Playfair Display', serif;
    
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--white-color);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--gray-color);
}

.lead {
    font-size: 1.25rem;
    font-weight: 400;
    color: var(--dark-color);
}

/* Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

/* Buttons */
.btn {
    padding: 12px 30px;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    border: 2px solid transparent;
    display: inline-block;
    text-align: center;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white-color);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn-outline {
    background-color: transparent;
    color: var(--white-color);
    border-color: var(--white-color);
}

.btn-outline:hover {
    background-color: var(--white-color);
    color: var(--primary-color);
}

.btn-light {
    background-color: var(--white-color);
    color: var(--primary-color);
    border-color: var(--white-color);
}

.btn-light:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* Top Bar */
.top-bar {
    background-color: var(--dark-color);
    color: var(--white-color);
    padding: 8px 0;
    font-size: 14px;
}

.contact-info {
    display: flex;
    gap: 20px;
}

.contact-item {
    color: var(--white-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.contact-item:hover {
    color: var(--secondary-color);
}

.social-links {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.social-link {
    color: var(--white-color);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: var(--white-color);
    transform: translateY(-2px);
}

/* Navigation */
.navbar {
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.navbar.scrolled {
    padding: 10px 0;
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
}

.navbar-brand .logo {
    height: 50px;
    width: auto;
}

.navbar-nav .nav-link {
    color: var(--dark-color);
    font-weight: 500;
    padding: 10px 15px;
    position: relative;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 80%;
}

.navbar-cta .btn {
    padding: 8px 20px;
    font-size: 14px;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    overflow: hidden;
}

.hero-slider {
    position: relative;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    align-items: center;
}

.hero-slide.active {
    opacity: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 90, 160, 0.8), rgba(44, 62, 80, 0.6));
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--white-color);
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 30px;
    z-index: 3;
}

.hero-prev,
.hero-next {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--white-color);
    font-size: 24px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.hero-prev:hover,
.hero-next:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.hero-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: var(--transition);
}

.indicator.active {
    background: var(--white-color);
}

/* Sections */
.section-header {
    margin-bottom: 4rem;
}

.section-title {
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--gray-color);
    max-width: 800px;
    margin: 0 auto;
}

/* About Section */
.about-section {
    padding: 80px 0;
}

.about-content {
    padding-right: 2rem;
}

.about-features {
    margin: 2rem 0;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 1.5rem;
}

.feature-item i {
    color: var(--primary-color);
    font-size: 24px;
    margin-top: 5px;
}

.feature-item h4 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.about-image {
    position: relative;
}

.about-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.experience-badge {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: var(--primary-color);
    color: var(--white-color);
    padding: 20px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--box-shadow);
}

.experience-badge .number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
}

.experience-badge .text {
    font-size: 14px;
    text-transform: uppercase;
}

/* Services Section */
.services-section {
    padding: 80px 0;
}

.service-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.service-icon {
    margin-bottom: 1.5rem;
}

.service-icon i {
    font-size: 3rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.service-card:hover .service-icon i {
    color: var(--secondary-color);
    transform: scale(1.1);
}

.service-title {
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.service-description {
    margin-bottom: 1.5rem;
}

.service-link {
    color: var(--primary-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.service-link:hover {
    color: var(--secondary-color);
}

.service-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.service-card:hover .service-image img {
    transform: scale(1.1);
}

/* Projects Section */
.projects-section {
    padding: 80px 0;
}

.project-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.project-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(44, 90, 160, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-link {
    color: var(--white-color);
    font-size: 24px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.project-link:hover {
    background: var(--white-color);
    color: var(--primary-color);
    transform: scale(1.1);
}

.project-content {
    padding: 1.5rem;
}

.project-category {
    color: var(--primary-color);
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.project-title {
    margin-bottom: 1rem;
}

.project-title a {
    color: var(--dark-color);
}

.project-title a:hover {
    color: var(--primary-color);
}

.project-description {
    margin-bottom: 1rem;
}

.project-location {
    color: var(--gray-color);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Contact CTA Section */
.contact-cta {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
}

.cta-title {
    color: var(--white-color);
    margin-bottom: 0.5rem;
}

.cta-subtitle {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
}

/* Footer */
.footer {
    background-color: var(--dark-color);
}

.footer-main {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-logo .logo {
    height: 40px;
    width: auto;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
}

.footer-contact .contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
}

.footer-contact .contact-item i {
    color: var(--primary-color);
    width: 20px;
}

.footer-contact .contact-item a {
    color: rgba(255, 255, 255, 0.8);
}

.footer-contact .contact-item a:hover {
    color: var(--white-color);
}

.footer-title {
    color: var(--white-color);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--white-color);
    padding-left: 5px;
}

.footer-social {
    display: flex;
    gap: 15px;
    margin-bottom: 1.5rem;
}

.footer-social .social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white-color);
    transition: var(--transition);
}

.footer-social .social-link:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.newsletter-form .input-group {
    margin-bottom: 0;
}

.newsletter-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white-color);
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-form .btn {
    border: 1px solid var(--primary-color);
}

.footer-bottom {
    padding: 1rem 0;
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.8);
}

.footer-legal {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
}

.footer-legal a {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.footer-legal a:hover {
    color: var(--white-color);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white-color);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .about-content {
        padding-right: 0;
        margin-bottom: 2rem;
    }
    
    .contact-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .social-links {
        justify-content: flex-start;
        margin-top: 10px;
    }
    
    .footer-legal {
        justify-content: flex-start;
        margin-top: 10px;
    }
    
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.75rem; }
}
