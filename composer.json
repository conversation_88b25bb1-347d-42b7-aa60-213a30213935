{"name": "flori-construction/web-app", "description": "Flori Construction Ltd - Professional Website and Admin System", "type": "project", "license": "proprietary", "authors": [{"name": "Flori Construction Ltd", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "firebase/php-jwt": "^6.0", "phpmailer/phpmailer": "^6.8", "intervention/image": "^2.7", "vlucas/phpdotenv": "^5.5", "monolog/monolog": "^3.0", "respect/validation": "^2.2"}, "require-dev": {"phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.7"}, "autoload": {"psr-4": {"FloriConstruction\\": "classes/", "FloriConstruction\\API\\": "api/classes/", "FloriConstruction\\Admin\\": "admin/classes/"}, "files": ["config/config.php"]}, "autoload-dev": {"psr-4": {"FloriConstruction\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "cs-check": "phpcs --standard=PSR12 classes/ api/ admin/", "cs-fix": "phpcbf --standard=PSR12 classes/ api/ admin/", "install-db": "mysql -u root -p < database/schema.sql && mysql -u root -p < database/seed_data.sql"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}