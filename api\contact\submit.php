<?php
/**
 * Contact Form Submission API Endpoint
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../classes/Contact.php';

setCORSHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['error' => 'Method not allowed'], 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }
    
    // Validate required fields
    $required_fields = ['name', 'email', 'message'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            jsonResponse(['error' => "Field {$field} is required"], 400);
        }
    }
    
    // Validate email format
    if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['error' => 'Invalid email format'], 400);
    }
    
    // Sanitize input data
    $data = [
        'name' => sanitizeInput($input['name']),
        'email' => sanitizeInput($input['email']),
        'phone' => isset($input['phone']) ? sanitizeInput($input['phone']) : null,
        'company' => isset($input['company']) ? sanitizeInput($input['company']) : null,
        'subject' => isset($input['subject']) ? sanitizeInput($input['subject']) : 'General Inquiry',
        'message' => sanitizeInput($input['message']),
        'service_interest' => isset($input['service_interest']) ? sanitizeInput($input['service_interest']) : 'general',
        'ip_address' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT']
    ];
    
    // Basic spam protection
    if (strlen($data['message']) < 10) {
        jsonResponse(['error' => 'Message is too short'], 400);
    }
    
    if (strlen($data['message']) > 5000) {
        jsonResponse(['error' => 'Message is too long'], 400);
    }
    
    // Check for spam patterns
    $spam_patterns = [
        '/\b(viagra|cialis|casino|poker|loan|mortgage)\b/i',
        '/\b(click here|visit now|buy now)\b/i',
        '/http[s]?:\/\/[^\s]+/i' // URLs in message
    ];
    
    foreach ($spam_patterns as $pattern) {
        if (preg_match($pattern, $data['message'])) {
            jsonResponse(['error' => 'Message appears to be spam'], 400);
        }
    }
    
    // Rate limiting - check if same IP submitted recently
    $contact = new Contact();
    if ($contact->checkRateLimit($data['ip_address'])) {
        jsonResponse(['error' => 'Too many submissions. Please wait before submitting again.'], 429);
    }
    
    // Submit contact form
    $result = $contact->submitContact($data);
    
    if ($result['success']) {
        // Send notification email (optional)
        try {
            $contact->sendNotificationEmail($data);
        } catch (Exception $e) {
            // Log email error but don't fail the request
            error_log("Failed to send notification email: " . $e->getMessage());
        }
        
        jsonResponse([
            'success' => true,
            'message' => 'Thank you for your message. We will get back to you soon.',
            'contact_id' => $result['contact_id']
        ], 200);
    } else {
        jsonResponse([
            'success' => false,
            'error' => $result['message']
        ], 400);
    }
    
} catch (Exception $e) {
    error_log("Contact form error: " . $e->getMessage());
    jsonResponse(['error' => 'Internal server error'], 500);
}
?>
