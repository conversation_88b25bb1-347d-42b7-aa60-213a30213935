# Flori Construction Ltd - Working .htaccess Configuration
# This version is tested and working

# Prevent directory browsing
Options -Indexes

# Hide sensitive files (Apache 2.4 syntax)
<Files "*.sql">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files ".htaccess">
    Require all denied
</Files>

<Files "composer.*">
    Require all denied
</Files>

<Files "*.ini">
    Require all denied
</Files>

<Files "*.conf">
    Require all denied
</Files>

# URL Rewriting (only if mod_rewrite is available)
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # API Routes
    RewriteRule ^api/auth/login/?$ api/auth/login.php [L,QSA]
    RewriteRule ^api/projects/?$ api/projects/index.php [L,QSA]
    RewriteRule ^api/services/?$ api/services/index.php [L,QSA]
    RewriteRule ^api/contact/?$ api/contact/submit.php [L,QSA]
    
    # Website Routes
    RewriteRule ^about/?$ about.php [L,QSA]
    RewriteRule ^services/?$ services.php [L,QSA]
    RewriteRule ^projects/?$ projects.php [L,QSA]
    RewriteRule ^contact/?$ contact.php [L,QSA]
    
    # Admin Routes
    RewriteRule ^admin/?$ admin/login.php [L,QSA]
    RewriteRule ^admin/login/?$ admin/login.php [L,QSA]
    
    # Prevent PHP execution in uploads directory
    RewriteRule ^uploads/.*\.php$ - [F,L]
</IfModule>

# Security Headers (only if mod_headers is available)
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Compression (only if mod_deflate is available)
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Browser Caching (only if mod_expires is available)
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>
