
-------------------------------------------------------------------------------
| GENERATORTEST CODING STANDARD: CODE COMPARISON ONLY, MISSING STANDARD BLOCK |
-------------------------------------------------------------------------------

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: Lorem ipsum dolor sit amet.             | Invalid: Maecenas non rutrum dolor.             |
----------------------------------------------------------------------------------------------------
| class Code {}                                  | class Comparison {}                             |
----------------------------------------------------------------------------------------------------


----------------------------------------------------------------------
| GENERATORTEST CODING STANDARD: ONE STANDARD BLOCK, CODE COMPARISON |
----------------------------------------------------------------------

Documentation contains one standard block and one code comparison.

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: Lorem ipsum dolor sit amet.             | Invalid: Maecenas non rutrum dolor.             |
----------------------------------------------------------------------------------------------------
| class Code {}                                  | class Comparison {}                             |
----------------------------------------------------------------------------------------------------


--------------------------------------------------------------
| GENERATORTEST CODING STANDARD: ONE STANDARD BLOCK, NO CODE |
--------------------------------------------------------------

Documentation contains one standard block and no code comparison.


---------------------------------------------------------------------------
| GENERATORTEST CODING STANDARD: ONE STANDARD BLOCK, TWO CODE COMPARISONS |
---------------------------------------------------------------------------

Documentation contains one standard block and two code comparisons.

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: Etiam commodo magna at vestibulum       | Invalid: Vivamus lacinia ante velit.            |
| blandit.                                       |                                                 |
----------------------------------------------------------------------------------------------------
| class Code {}                                  | class Comparison {}                             |
----------------------------------------------------------------------------------------------------

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: Pellentesque nisi neque.                | Invalid: Mauris dictum metus quis maximus       |
|                                                | pharetra.                                       |
----------------------------------------------------------------------------------------------------
| $one = 10;                                     | $a = 10;                                        |
----------------------------------------------------------------------------------------------------


---------------------------------------------------------------
| GENERATORTEST CODING STANDARD: TWO STANDARD BLOCKS, NO CODE |
---------------------------------------------------------------

This is standard block one.

This is standard block two.


---------------------------------------------------------------------------
| GENERATORTEST CODING STANDARD: TWO STANDARD BLOCKS, ONE CODE COMPARISON |
---------------------------------------------------------------------------

This is standard block one.

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: Vestibulum et orci condimentum.         | Invalid: Donec in nisl ut tortor convallis      |
|                                                | interdum.                                       |
----------------------------------------------------------------------------------------------------
| class Code {}                                  | class Comparison {}                             |
----------------------------------------------------------------------------------------------------

This is standard block two.


------------------------------------------------------------------------------
| GENERATORTEST CODING STANDARD: TWO STANDARD BLOCKS, THREE CODE COMPARISONS |
------------------------------------------------------------------------------

This is standard block one.

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: Vestibulum et orci condimentum.         | Invalid: Donec in nisl ut tortor convallis      |
|                                                | interdum.                                       |
----------------------------------------------------------------------------------------------------
| class Code {}                                  | class Comparison {}                             |
----------------------------------------------------------------------------------------------------

This is standard block two.

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: Pellentesque nisi neque.                | Invalid: Mauris dictum metus quis maximus       |
|                                                | pharetra.                                       |
----------------------------------------------------------------------------------------------------
| $one = 10;                                     | $a = 10;                                        |
----------------------------------------------------------------------------------------------------

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: Quisque sagittis nisi vitae.            | Invalid: Morbi ac libero vitae lorem.           |
----------------------------------------------------------------------------------------------------
| echo $foo;                                     | print $foo;                                     |
----------------------------------------------------------------------------------------------------

