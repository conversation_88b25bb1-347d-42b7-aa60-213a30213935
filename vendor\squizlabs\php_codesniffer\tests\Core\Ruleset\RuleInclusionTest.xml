<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="RuleInclusionTest" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

    <rule ref="PSR2">
        <properties>
            <property name="setforallsniffs" value="true" />
            <property name="indent" value="20" />
        </properties>
    </rule>

    <rule ref="PSR12.Operators">
        <properties>
            <property name="setforallincategory" value="true" />
            <property name="ignoreSpacingBeforeAssignments" value="false" />
        </properties>
    </rule>

    <rule ref="Generic.Arrays.ArrayIndent">
        <properties>
            <property name="indent" value="2" />
        </properties>
    </rule>

    <rule ref="Generic.Metrics.CyclomaticComplexity.MaxExceeded">
        <properties>
            <property name="complexity" value="50" />
        </properties>
    </rule>

    <!-- Sniff directory include. -->
    <rule ref="./src/Standards/Squiz/Sniffs/Files/">
        <properties>
            <property name="setforsquizfilessniffs" value="true" />
        </properties>
    </rule>

    <!-- Sniff file include. -->
    <rule ref="./src/Standards/Generic/Sniffs/Files/LineLengthSniff.php">
        <properties>
            <property name="lineLimit" value="10" />
        </properties>
    </rule>

    <rule ref="./../%path_root_dir%/src/Standards/Generic/Sniffs/NamingConventions/CamelCapsFunctionNameSniff.php">
        <properties>
            <property name="strict" value="false" />
        </properties>
    </rule>

    <!-- Ruleset file include. -->
    <rule ref="./RuleInclusionTest-include.xml">
        <!-- Property being set for all sniffs included in this ruleset. -->
        <properties>
            <property name="absoluteNestingLevel" value="true" />
        </properties>
    </rule>

</ruleset>
