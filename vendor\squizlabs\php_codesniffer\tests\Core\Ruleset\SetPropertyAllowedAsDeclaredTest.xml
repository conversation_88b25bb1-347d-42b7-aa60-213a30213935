<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="SetSniffPropertyTest" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

    <rule ref="./tests/Core/Ruleset/Fixtures/TestStandard/Sniffs/SetProperty/AllowedAsDeclaredSniff.php">
        <properties>
            <property name="arbitrarystring" value="arbitraryvalue"/>
            <property name="arbitraryarray" type="array">
                <element key="mykey" value="myvalue"/>
            </property>
            <property name="arbitraryarray" type="array" extend="true">
                <element key="otherkey" value="othervalue"/>
            </property>
        </properties>
    </rule>

</ruleset>
