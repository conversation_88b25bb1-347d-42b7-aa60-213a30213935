# GeneratorTest Coding Standard

## Code Comparison, blank lines

This is a standard block.
  <table>
   <tr>
    <th>Valid: Checking handling of blank lines.</th>
    <th>Invalid: Checking handling of blank lines.</th>
   </tr>
   <tr>
<td>

    // First line of the code sample is
    // deliberately empty.
    
    // We also have a blank line in the middle.
    
    // And a blank line at the end.

</td>
<td>

    // First line of the code sample is
    // deliberately empty.
    
    // We also have a blank line in the middle.
    
    // And a blank line at the end.

</td>
   </tr>
  </table>

Documentation generated on *REDACTED* by [PHP_CodeSniffer *VERSION*](https://github.com/PHPCSStandards/PHP_CodeSniffer)
