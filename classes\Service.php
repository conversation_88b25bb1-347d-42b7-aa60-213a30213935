<?php
/**
 * Service Class
 * Handles service management operations
 */

class Service {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Get all services
     */
    public function getServices($params = []) {
        try {
            $where_conditions = ["status = 'active'"];
            $bind_params = [];
            
            // Filter by featured
            if (isset($params['featured']) && $params['featured']) {
                $where_conditions[] = "is_featured = 1";
            }
            
            $where_clause = implode(' AND ', $where_conditions);
            
            $query = "SELECT id, title, slug, short_description, description, icon, 
                            featured_image, gallery_images, features, is_featured, 
                            meta_title, meta_description, sort_order, created_at
                     FROM services 
                     WHERE {$where_clause}
                     ORDER BY sort_order ASC, created_at DESC";
            
            $stmt = $this->db->prepare($query);
            foreach ($bind_params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            
            $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Process JSON fields
            foreach ($services as &$service) {
                $service['gallery_images'] = json_decode($service['gallery_images'], true) ?: [];
                $service['features'] = json_decode($service['features'], true) ?: [];
            }
            
            return [
                'success' => true,
                'data' => $services
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch services: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get single service by ID or slug
     */
    public function getService($identifier, $by_slug = false) {
        try {
            $field = $by_slug ? 'slug' : 'id';
            
            $query = "SELECT * FROM services WHERE {$field} = :identifier AND status = 'active'";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':identifier', $identifier);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $service = $stmt->fetch(PDO::FETCH_ASSOC);
                $service['gallery_images'] = json_decode($service['gallery_images'], true) ?: [];
                $service['features'] = json_decode($service['features'], true) ?: [];
                
                return [
                    'success' => true,
                    'data' => $service
                ];
            }
            
            return ['success' => false, 'message' => 'Service not found'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch service: ' . $e->getMessage()];
        }
    }
    
    /**
     * Create new service
     */
    public function createService($data, $created_by) {
        try {
            // Validate required fields
            $required_fields = ['title'];
            foreach ($required_fields as $field) {
                if (empty($data[$field])) {
                    return ['success' => false, 'message' => "Field {$field} is required"];
                }
            }
            
            // Generate slug
            $slug = $this->generateUniqueSlug($data['title']);
            
            // Prepare JSON fields
            $gallery_images = isset($data['gallery_images']) ? json_encode($data['gallery_images']) : null;
            $features = isset($data['features']) ? json_encode($data['features']) : null;
            
            $query = "INSERT INTO services (title, slug, short_description, description, icon, 
                                          featured_image, gallery_images, features, is_featured, 
                                          meta_title, meta_description, sort_order, created_by) 
                     VALUES (:title, :slug, :short_description, :description, :icon, 
                            :featured_image, :gallery_images, :features, :is_featured, 
                            :meta_title, :meta_description, :sort_order, :created_by)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':title', $data['title']);
            $stmt->bindParam(':slug', $slug);
            $stmt->bindParam(':short_description', $data['short_description'] ?? null);
            $stmt->bindParam(':description', $data['description'] ?? null);
            $stmt->bindParam(':icon', $data['icon'] ?? null);
            $stmt->bindParam(':featured_image', $data['featured_image'] ?? null);
            $stmt->bindParam(':gallery_images', $gallery_images);
            $stmt->bindParam(':features', $features);
            $stmt->bindParam(':is_featured', $data['is_featured'] ?? false, PDO::PARAM_BOOL);
            $stmt->bindParam(':meta_title', $data['meta_title'] ?? $data['title']);
            $stmt->bindParam(':meta_description', $data['meta_description'] ?? $data['short_description']);
            $stmt->bindParam(':sort_order', $data['sort_order'] ?? 0);
            $stmt->bindParam(':created_by', $created_by);
            
            if ($stmt->execute()) {
                $service_id = $this->db->lastInsertId();
                return [
                    'success' => true,
                    'message' => 'Service created successfully',
                    'service_id' => $service_id
                ];
            }
            
            return ['success' => false, 'message' => 'Failed to create service'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Service creation failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update service
     */
    public function updateService($id, $data, $updated_by) {
        try {
            // Check if service exists
            $existing = $this->getService($id);
            if (!$existing['success']) {
                return ['success' => false, 'message' => 'Service not found'];
            }
            
            // Generate new slug if title changed
            if (isset($data['title']) && $data['title'] !== $existing['data']['title']) {
                $data['slug'] = $this->generateUniqueSlug($data['title'], $id);
            }
            
            // Prepare JSON fields
            if (isset($data['gallery_images'])) {
                $data['gallery_images'] = json_encode($data['gallery_images']);
            }
            if (isset($data['features'])) {
                $data['features'] = json_encode($data['features']);
            }
            
            // Build update query dynamically
            $update_fields = [];
            $bind_params = [':id' => $id];
            
            $allowed_fields = ['title', 'slug', 'short_description', 'description', 'icon', 
                             'featured_image', 'gallery_images', 'features', 'is_featured', 
                             'meta_title', 'meta_description', 'sort_order', 'status'];
            
            foreach ($allowed_fields as $field) {
                if (isset($data[$field])) {
                    $update_fields[] = "{$field} = :{$field}";
                    $bind_params[":{$field}"] = $data[$field];
                }
            }
            
            if (empty($update_fields)) {
                return ['success' => false, 'message' => 'No fields to update'];
            }
            
            $query = "UPDATE services SET " . implode(', ', $update_fields) . " WHERE id = :id";
            $stmt = $this->db->prepare($query);
            
            foreach ($bind_params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'Service updated successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to update service'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Service update failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Delete service
     */
    public function deleteService($id) {
        try {
            $query = "UPDATE services SET status = 'inactive' WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            
            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'Service deleted successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to delete service'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Service deletion failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Generate unique slug
     */
    private function generateUniqueSlug($title, $exclude_id = null) {
        $slug = createSlug($title);
        $original_slug = $slug;
        $counter = 1;
        
        while ($this->slugExists($slug, $exclude_id)) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * Check if slug exists
     */
    private function slugExists($slug, $exclude_id = null) {
        $query = "SELECT id FROM services WHERE slug = :slug";
        if ($exclude_id) {
            $query .= " AND id != :exclude_id";
        }
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':slug', $slug);
        if ($exclude_id) {
            $stmt->bindParam(':exclude_id', $exclude_id);
        }
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Get featured services
     */
    public function getFeaturedServices($limit = 5) {
        try {
            $query = "SELECT id, title, slug, short_description, icon, featured_image, sort_order
                     FROM services 
                     WHERE status = 'active' AND is_featured = 1
                     ORDER BY sort_order ASC, created_at DESC 
                     LIMIT :limit";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            return [
                'success' => true,
                'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch featured services: ' . $e->getMessage()];
        }
    }
}
?>
