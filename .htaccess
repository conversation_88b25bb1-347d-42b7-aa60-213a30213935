# Flori Construction Ltd - Apache Configuration
# Security and URL Rewriting Rules

# Enable URL Rewriting
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Hide sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "(\.sql|\.log|\.ini|\.conf)$">
    Order allow,deny
    Deny from all
</Files>

# Protect config directory
<Directory "config">
    Order allow,deny
    Deny from all
</Directory>

# API Routes
RewriteRule ^api/auth/login/?$ api/auth/login.php [L,QSA]
RewriteRule ^api/auth/register/?$ api/auth/register.php [L,QSA]
RewriteRule ^api/auth/logout/?$ api/auth/logout.php [L,QSA]
RewriteRule ^api/projects/?$ api/projects/index.php [L,QSA]
RewriteRule ^api/projects/([0-9]+)/?$ api/projects/single.php?id=$1 [L,QSA]
RewriteRule ^api/services/?$ api/services/index.php [L,QSA]
RewriteRule ^api/services/([a-zA-Z0-9-]+)/?$ api/services/single.php?slug=$1 [L,QSA]
RewriteRule ^api/media/?$ api/media/index.php [L,QSA]
RewriteRule ^api/contact/?$ api/contact/submit.php [L,QSA]
RewriteRule ^api/admin/dashboard/?$ api/admin/dashboard.php [L,QSA]
RewriteRule ^api/admin/projects/?$ api/admin/projects.php [L,QSA]
RewriteRule ^api/admin/services/?$ api/admin/services.php [L,QSA]
RewriteRule ^api/admin/media/?$ api/admin/media.php [L,QSA]
RewriteRule ^api/admin/contacts/?$ api/admin/contacts.php [L,QSA]
RewriteRule ^api/admin/settings/?$ api/admin/settings.php [L,QSA]

# Website Routes
RewriteRule ^about/?$ about.php [L,QSA]
RewriteRule ^services/?$ services.php [L,QSA]
RewriteRule ^services/([a-zA-Z0-9-]+)/?$ service-detail.php?slug=$1 [L,QSA]
RewriteRule ^projects/?$ projects.php [L,QSA]
RewriteRule ^projects/([a-zA-Z0-9-]+)/?$ project-detail.php?slug=$1 [L,QSA]
RewriteRule ^media/?$ media.php [L,QSA]
RewriteRule ^contact/?$ contact.php [L,QSA]

# Admin Routes
RewriteRule ^admin/?$ admin/index.php [L,QSA]
RewriteRule ^admin/login/?$ admin/login.php [L,QSA]
RewriteRule ^admin/logout/?$ admin/logout.php [L,QSA]
RewriteRule ^admin/dashboard/?$ admin/dashboard.php [L,QSA]
RewriteRule ^admin/projects/?$ admin/projects/index.php [L,QSA]
RewriteRule ^admin/projects/add/?$ admin/projects/add.php [L,QSA]
RewriteRule ^admin/projects/edit/([0-9]+)/?$ admin/projects/edit.php?id=$1 [L,QSA]
RewriteRule ^admin/services/?$ admin/services/index.php [L,QSA]
RewriteRule ^admin/services/add/?$ admin/services/add.php [L,QSA]
RewriteRule ^admin/services/edit/([0-9]+)/?$ admin/services/edit.php?id=$1 [L,QSA]
RewriteRule ^admin/media/?$ admin/media/index.php [L,QSA]
RewriteRule ^admin/media/upload/?$ admin/media/upload.php [L,QSA]
RewriteRule ^admin/contacts/?$ admin/contacts/index.php [L,QSA]
RewriteRule ^admin/settings/?$ admin/settings/index.php [L,QSA]

# File Upload Security
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    <IfModule mod_dir.c>
        DirectoryIndex disabled
    </IfModule>
</FilesMatch>

# Prevent access to uploads directory PHP files
<Directory "uploads">
    <FilesMatch "\.php$">
        Order allow,deny
        Deny from all
    </FilesMatch>
</Directory>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Error Pages
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# Prevent directory browsing
Options -Indexes

# Force HTTPS (uncomment for production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
