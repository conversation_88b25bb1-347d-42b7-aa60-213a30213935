# Flori Construction Ltd - Website & Admin System

A comprehensive construction company website with admin panel and mobile app for Flori Construction Ltd.

## Features

### Website
- Modern, responsive design
- Homepage with hero slider and featured content
- About Us page with company information
- Services showcase with detailed descriptions
- Project portfolio with filtering and categories
- Media gallery for photos and videos
- Contact form with validation and spam protection
- SEO optimized with structured data

### Admin Panel (Web)
- Secure authentication with JWT tokens
- Dashboard with analytics and statistics
- Project management (CRUD operations)
- Service management
- Media library with file upload
- Contact message management
- User management
- Settings configuration

### Mobile Admin App (React Native)
- Cross-platform iOS and Android support
- Same functionality as web admin panel
- Photo upload from mobile device
- Push notifications for new messages
- Offline capability for viewing data

## Technology Stack

### Backend
- **PHP 7.4+** - Server-side scripting
- **MySQL** - Database management
- **JWT** - Authentication tokens
- **PHPMailer** - Email functionality
- **Intervention Image** - Image processing

### Frontend
- **HTML5/CSS3** - Modern web standards
- **Bootstrap 5** - Responsive framework
- **JavaScript ES6+** - Interactive functionality
- **Font Awesome** - Icons
- **Google Fonts** - Typography

### Mobile
- **React Native** - Cross-platform mobile development
- **React Navigation** - Navigation system
- **AsyncStorage** - Local data storage
- **React Native Image Picker** - Photo upload

## Installation

### Prerequisites
- XAMPP/WAMP/LAMP server
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Composer (PHP package manager)
- Node.js and npm (for mobile app)

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   # Place the project in your web server directory
   # For XAMPP: C:\xampp\htdocs\flori-construction-web-app
   ```

2. **Database Setup**
   ```bash
   # Create database and import schema
   mysql -u root -p
   CREATE DATABASE flori_construction;
   exit
   
   # Import database schema and seed data
   mysql -u root -p flori_construction < database/schema.sql
   mysql -u root -p flori_construction < database/seed_data.sql
   ```

3. **Install PHP Dependencies**
   ```bash
   composer install
   ```

4. **Configure Database**
   - Edit `config/database.php`
   - Update database credentials if needed
   ```php
   private $host = 'localhost';
   private $db_name = 'flori_construction';
   private $username = 'root';
   private $password = '';
   ```

5. **Configure Application**
   - Edit `config/config.php`
   - Update site URL and other settings
   ```php
   define('SITE_URL', 'http://localhost/flori-construction-web-app');
   ```

6. **Set Permissions**
   ```bash
   # Make uploads directory writable
   chmod 755 uploads/
   chmod 755 assets/images/
   ```

7. **Access the Application**
   - Website: `http://localhost/flori-construction-web-app`
   - Admin Panel: `http://localhost/flori-construction-web-app/admin`
   - Default admin credentials:
     - Username: `admin`
     - Password: `admin123`

## Mobile App Setup

### Prerequisites
- Node.js 16+
- React Native CLI
- Android Studio (for Android)
- Xcode (for iOS, macOS only)

### Installation
```bash
cd mobile-admin
npm install

# For iOS
cd ios && pod install && cd ..

# Run the app
npx react-native run-android
# or
npx react-native run-ios
```

## API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/verify` - Verify token

### Project Endpoints
- `GET /api/projects` - Get all projects
- `GET /api/projects/{id}` - Get single project
- `POST /api/projects` - Create project (admin)
- `PUT /api/projects/{id}` - Update project (admin)
- `DELETE /api/projects/{id}` - Delete project (admin)

### Service Endpoints
- `GET /api/services` - Get all services
- `GET /api/services/{slug}` - Get single service
- `POST /api/services` - Create service (admin)
- `PUT /api/services/{id}` - Update service (admin)
- `DELETE /api/services/{id}` - Delete service (admin)

### Contact Endpoints
- `POST /api/contact/submit` - Submit contact form
- `GET /api/admin/contacts` - Get all contacts (admin)

## File Structure

```
flori-construction-web-app/
├── api/                    # API endpoints
│   ├── auth/              # Authentication
│   ├── projects/          # Project management
│   ├── services/          # Service management
│   ├── contact/           # Contact handling
│   └── admin/             # Admin endpoints
├── admin/                 # Admin panel
│   ├── login.php          # Admin login
│   ├── dashboard.php      # Admin dashboard
│   └── ...
├── assets/                # Static assets
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   └── images/            # Images
├── classes/               # PHP classes
│   ├── Auth.php           # Authentication class
│   ├── Project.php        # Project management
│   ├── Service.php        # Service management
│   └── Contact.php        # Contact handling
├── config/                # Configuration files
│   ├── config.php         # Main configuration
│   └── database.php       # Database configuration
├── database/              # Database files
│   ├── schema.sql         # Database schema
│   └── seed_data.sql      # Initial data
├── includes/              # Common includes
│   ├── header.php         # Site header
│   └── footer.php         # Site footer
├── mobile-admin/          # React Native mobile app
├── uploads/               # File uploads
├── index.php              # Homepage
├── about.php              # About page
├── services.php           # Services page
├── projects.php           # Projects page
├── contact.php            # Contact page
├── .htaccess              # Apache configuration
├── composer.json          # PHP dependencies
└── README.md              # This file
```

## Security Features

- JWT token authentication
- SQL injection prevention with prepared statements
- XSS protection with input sanitization
- CSRF protection
- Rate limiting for contact forms
- File upload validation
- Secure password hashing
- Input validation and sanitization

## SEO Features

- Clean, semantic HTML structure
- Meta tags optimization
- Open Graph and Twitter Card support
- Structured data (JSON-LD)
- XML sitemap generation
- Friendly URLs with .htaccess
- Image optimization and lazy loading
- Page speed optimization

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is proprietary software for Flori Construction Ltd.

## Support

For technical support or questions:
- Email: <EMAIL>
- Phone: 0208 914 7883

## Changelog

### Version 1.0.0 (2024)
- Initial release
- Complete website with admin panel
- Mobile admin app
- API implementation
- Security features
- SEO optimization
