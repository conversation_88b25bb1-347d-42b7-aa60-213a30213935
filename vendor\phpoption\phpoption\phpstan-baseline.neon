parameters:
	ignoreErrors:
		-
			message: "#^Method PhpOption\\\\Option\\:\\:ensure\\(\\) should return PhpOption\\\\Option\\<S\\> but returns PhpOption\\\\LazyOption\\<mixed\\>\\.$#"
			count: 1
			path: src/PhpOption/Option.php

		-
			message: "#^Method PhpOption\\\\Option\\:\\:fromReturn\\(\\) has parameter \\$arguments with no value type specified in iterable type array\\.$#"
			count: 1
			path: src/PhpOption/Option.php

		-
			message: "#^Method PhpOption\\\\Option\\:\\:fromReturn\\(\\) should return PhpOption\\\\LazyOption\\<S\\> but returns PhpOption\\\\LazyOption\\<mixed\\>\\.$#"
			count: 1
			path: src/PhpOption/Option.php

		-
			message: "#^Parameter \\#1 \\$callback of function array_map expects \\(callable\\(mixed\\)\\: mixed\\)\\|null, Closure\\(PhpOption\\\\Option\\)\\: T given\\.$#"
			count: 1
			path: src/PhpOption/Option.php

		-
			message: "#^Parameter \\#2 \\$callback of function array_reduce expects callable\\(bool\\|TReturn, mixed\\)\\: \\(bool\\|TReturn\\), Closure\\(mixed, PhpOption\\\\Option\\)\\: \\(bool\\|TReturn\\) given\\.$#"
			count: 1
			path: src/PhpOption/Option.php

		-
			message: "#^Template type S of method PhpOption\\\\Option\\:\\:lift\\(\\) is not referenced in a parameter\\.$#"
			count: 1
			path: src/PhpOption/Option.php

