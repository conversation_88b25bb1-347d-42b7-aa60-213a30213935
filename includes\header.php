<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? htmlspecialchars($pageTitle) . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    
    <!-- Meta Tags -->
    <meta name="description" content="<?php echo isset($pageDescription) ? htmlspecialchars($pageDescription) : 'Professional construction services in London - Civil Engineering, Groundworks, RC Frames, Basements, Hard Landscaping'; ?>">
    <meta name="keywords" content="construction, civil engineering, groundworks, RC frames, basements, hard landscaping, London, building, contractor">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($pageTitle) ? htmlspecialchars($pageTitle) : SITE_NAME; ?>">
    <meta property="og:description" content="<?php echo isset($pageDescription) ? htmlspecialchars($pageDescription) : 'Professional construction services in London'; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/logo/flori-construction-og.jpg">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo isset($pageTitle) ? htmlspecialchars($pageTitle) : SITE_NAME; ?>">
    <meta name="twitter:description" content="<?php echo isset($pageDescription) ? htmlspecialchars($pageDescription) : 'Professional construction services in London'; ?>">
    <meta name="twitter:image" content="<?php echo SITE_URL; ?>/assets/images/logo/flori-construction-og.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo SITE_URL; ?>/assets/images/apple-touch-icon.png">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "<?php echo SITE_NAME; ?>",
        "url": "<?php echo SITE_URL; ?>",
        "logo": "<?php echo SITE_URL; ?>/assets/images/logo/flori-construction-logo.png",
        "description": "Professional construction services in London including civil engineering, groundworks, RC frames, basements, and hard landscaping.",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "662 High Road North Finchley",
            "addressLocality": "London",
            "postalCode": "N12 0NL",
            "addressCountry": "GB"
        },
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "<?php echo SITE_PHONE; ?>",
            "contactType": "customer service",
            "availableLanguage": "English"
        },
        "sameAs": [
            "<?php echo FACEBOOK_URL; ?>",
            "<?php echo INSTAGRAM_URL; ?>",
            "<?php echo YOUTUBE_URL; ?>",
            "<?php echo LINKEDIN_URL; ?>"
        ]
    }
    </script>
</head>
<body>
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="contact-info">
                        <a href="tel:<?php echo SITE_PHONE; ?>" class="contact-item">
                            <i class="fas fa-phone"></i>
                            <?php echo SITE_PHONE; ?>
                        </a>
                        <a href="mailto:<?php echo SITE_EMAIL; ?>" class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <?php echo SITE_EMAIL; ?>
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="social-links">
                        <a href="<?php echo FACEBOOK_URL; ?>" target="_blank" rel="noopener" class="social-link">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="<?php echo INSTAGRAM_URL; ?>" target="_blank" rel="noopener" class="social-link">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="<?php echo YOUTUBE_URL; ?>" target="_blank" rel="noopener" class="social-link">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="<?php echo LINKEDIN_URL; ?>" target="_blank" rel="noopener" class="social-link">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand" href="<?php echo SITE_URL; ?>">
                <img src="<?php echo SITE_URL; ?>/assets/images/logo/flori-construction-logo.png" alt="<?php echo SITE_NAME; ?>" class="logo">
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($_SERVER['REQUEST_URI'] == '/' || $_SERVER['REQUEST_URI'] == '/index.php') ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/about') !== false) ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/about">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/services') !== false) ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/services">Our Services</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?php echo (strpos($_SERVER['REQUEST_URI'], '/projects') !== false) ? 'active' : ''; ?>" href="#" id="projectsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Our Projects
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="projectsDropdown">
                            <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/projects">All Projects</a></li>
                            <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/projects?category=completed">Completed Projects</a></li>
                            <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/projects?category=ongoing">Ongoing Projects</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/media') !== false) ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/media">Media</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/contact') !== false) ? 'active' : ''; ?>" href="<?php echo SITE_URL; ?>/contact">Contact Us</a>
                    </li>
                </ul>
                
                <!-- CTA Button -->
                <div class="navbar-cta ms-3">
                    <a href="<?php echo SITE_URL; ?>/contact" class="btn btn-primary">Get Quote</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
