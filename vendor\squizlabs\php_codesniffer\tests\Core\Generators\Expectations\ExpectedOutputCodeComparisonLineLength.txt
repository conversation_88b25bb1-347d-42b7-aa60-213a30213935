
---------------------------------------------------------------
| GENERATORTEST CODING STANDARD: CODE COMPARISON, LINE LENGTH |
---------------------------------------------------------------

Ensure there is no PHP "Warning: str_repeat(): Second argument has to be greater than or equal to
0".
Ref: squizlabs/PHP_CodeSniffer#2522

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: contains line which is too long.        | Invalid: contains line which is too long.       |
----------------------------------------------------------------------------------------------------
| class Foo extends Bar implements Countable, Serializable| class Foo extends Bar                           |
| {                                              | {                                               |
| }                                              |     public static function foobar($param1, $param2) {}|
|                                                | }                                               |
----------------------------------------------------------------------------------------------------

