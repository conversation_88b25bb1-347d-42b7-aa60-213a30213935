name: Tests

on:
  push:
  pull_request:

jobs:
  latest:
    name: PHP ${{ matrix.php }} Latest
    runs-on: ubuntu-24.04

    strategy:
      matrix:
        php: ['7.2', '7.3', '7.4', '8.0', '8.1', '8.2', '8.3', '8.4']

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          tools: composer:v2
          coverage: none

      - name: Setup Problem Matchers
        run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Install Latest Dependencies
        uses: nick-invision/retry@v3
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer update --no-interaction --no-progress

      - name: Execute PHPUnit
        run: vendor/bin/phpunit

  lowest:
    name: PHP ${{ matrix.php }} Lowest
    runs-on: ubuntu-24.04

    strategy:
      matrix:
        php: ['7.2', '7.3', '7.4', '8.0', '8.1', '8.2', '8.3', '8.4']

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          tools: composer:v2
          coverage: none

      - name: Setup Problem Matchers
        run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Install Lowest Dependencies
        uses: nick-invision/retry@v3
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer update --prefer-lowest --prefer-stable --no-interaction --no-progress

      - name: Execute PHPUnit
        run: vendor/bin/phpunit
