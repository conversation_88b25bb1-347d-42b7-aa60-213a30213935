<?php
/**
 * Projects List API Endpoint
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../classes/Project.php';

setCORSHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['error' => 'Method not allowed'], 405);
}

try {
    // Get query parameters
    $params = [
        'page' => isset($_GET['page']) ? (int)$_GET['page'] : 1,
        'limit' => isset($_GET['limit']) ? (int)$_GET['limit'] : ITEMS_PER_PAGE,
        'category' => isset($_GET['category']) ? sanitizeInput($_GET['category']) : null,
        'service_type' => isset($_GET['service_type']) ? sanitizeInput($_GET['service_type']) : null,
        'search' => isset($_GET['search']) ? sanitizeInput($_GET['search']) : null,
        'featured' => isset($_GET['featured']) ? (bool)$_GET['featured'] : false
    ];
    
    // Remove null values
    $params = array_filter($params, function($value) {
        return $value !== null && $value !== '';
    });
    
    $project = new Project();
    
    // Get featured projects if requested
    if (isset($_GET['featured']) && $_GET['featured']) {
        $result = $project->getFeaturedProjects($params['limit']);
    } else {
        $result = $project->getProjects($params);
    }
    
    if ($result['success']) {
        jsonResponse([
            'success' => true,
            'data' => $result['data'],
            'pagination' => $result['pagination'] ?? null
        ], 200);
    } else {
        jsonResponse([
            'success' => false,
            'error' => $result['message']
        ], 400);
    }
    
} catch (Exception $e) {
    error_log("Projects API error: " . $e->getMessage());
    jsonResponse(['error' => 'Internal server error'], 500);
}
?>
