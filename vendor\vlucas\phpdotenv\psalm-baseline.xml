<?xml version="1.0" encoding="UTF-8"?>
<files psalm-version="6.10.0@9c0add4eb88d4b169ac04acb7c679918cbb9c252">
  <file src="src/Dotenv.php">
    <ClassMustBeFinal>
      <code><![CDATA[Dotenv]]></code>
    </ClassMustBeFinal>
  </file>
  <file src="src/Parser/EntryParser.php">
    <PossiblyFalseArgument>
      <code><![CDATA[\strtok($subject, "\n")]]></code>
    </PossiblyFalseArgument>
  </file>
  <file src="src/Repository/Adapter/EnvConstAdapter.php">
    <RedundantConditionGivenDocblockType>
      <code><![CDATA[\is_scalar($value)]]></code>
    </RedundantConditionGivenDocblockType>
  </file>
  <file src="src/Repository/RepositoryBuilder.php">
    <InvalidStringClass>
      <code><![CDATA[$adapter::create()]]></code>
      <code><![CDATA[$reader::create()]]></code>
      <code><![CDATA[$writer::create()]]></code>
    </InvalidStringClass>
    <MissingClosureReturnType>
      <code><![CDATA[static function ($adapter) {]]></code>
      <code><![CDATA[static function ($reader) {]]></code>
      <code><![CDATA[static function ($writer) {]]></code>
    </MissingClosureReturnType>
  </file>
  <file src="src/Util/Regex.php">
    <ArgumentTypeCoercion>
      <code><![CDATA[$pattern]]></code>
      <code><![CDATA[$pattern]]></code>
      <code><![CDATA[$pattern]]></code>
      <code><![CDATA[$pattern]]></code>
    </ArgumentTypeCoercion>
    <InvalidReturnStatement>
      <code><![CDATA[self::pregAndWrap(static function (string $subject) use ($pattern) {
            return (int) @\preg_match_all($pattern, $subject);
        }, $subject)]]></code>
    </InvalidReturnStatement>
    <InvalidReturnType>
      <code><![CDATA[\GrahamCampbell\ResultType\Result<int, string>]]></code>
    </InvalidReturnType>
  </file>
  <file src="src/Validator.php">
    <ClassMustBeFinal>
      <code><![CDATA[Validator]]></code>
    </ClassMustBeFinal>
  </file>
</files>
