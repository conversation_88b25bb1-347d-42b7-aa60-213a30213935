<documentation title="Zend Code Analyzer">
    <standard>
    <![CDATA[
    PHP Code should pass the zend code analyzer.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Valid PHP Code.">
        <![CDATA[
function foo($bar, $baz)
{
    return <em>$bar + $baz</em>;
}
        ]]>
        </code>
        <code title="Invalid: There is an unused function parameter.">
        <![CDATA[
function foo($bar, $baz)
{
    return <em>$bar + 2</em>;
}
        ]]>
        </code>
    </code_comparison>
</documentation>
