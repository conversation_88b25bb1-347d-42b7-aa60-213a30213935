<?php

function MyFunction1()
{
    // Some code goes here.
    echo $code;
}

function MyFunction2()
{

    // Some code goes here.
    echo $code;
}

class MyClass
{
    function MyFunction1()
    {
        // Some code goes here.
        echo $code;
    }

    function MyFunction2()
    {

        // Some code goes here.
        echo $code;
    }

    public function register();
}

function emptyFunction()
{

}//end emptyFunction();

public function register();

function allOnOneLine() { echo 'abc'; }

$a = function()
{
	echo 'Testing a closure';
};

$a = function()
{



	echo 'Testing a closure';
};
