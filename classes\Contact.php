<?php
/**
 * Contact Class
 * Handles contact form submissions and management
 */

class Contact {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Submit contact form
     */
    public function submitContact($data) {
        try {
            $query = "INSERT INTO contacts (name, email, phone, company, subject, message, 
                                          service_interest, ip_address, user_agent) 
                     VALUES (:name, :email, :phone, :company, :subject, :message, 
                            :service_interest, :ip_address, :user_agent)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':company', $data['company']);
            $stmt->bindParam(':subject', $data['subject']);
            $stmt->bindParam(':message', $data['message']);
            $stmt->bindParam(':service_interest', $data['service_interest']);
            $stmt->bindParam(':ip_address', $data['ip_address']);
            $stmt->bindParam(':user_agent', $data['user_agent']);
            
            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Contact form submitted successfully',
                    'contact_id' => $this->db->lastInsertId()
                ];
            }
            
            return ['success' => false, 'message' => 'Failed to submit contact form'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Contact submission failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get all contacts with pagination and filtering
     */
    public function getContacts($params = []) {
        try {
            $page = isset($params['page']) ? (int)$params['page'] : 1;
            $limit = isset($params['limit']) ? (int)$params['limit'] : ADMIN_ITEMS_PER_PAGE;
            $offset = ($page - 1) * $limit;
            
            $where_conditions = [];
            $bind_params = [];
            
            // Filter by status
            if (!empty($params['status'])) {
                $where_conditions[] = "status = :status";
                $bind_params[':status'] = $params['status'];
            }
            
            // Filter by service interest
            if (!empty($params['service_interest'])) {
                $where_conditions[] = "service_interest = :service_interest";
                $bind_params[':service_interest'] = $params['service_interest'];
            }
            
            // Search functionality
            if (!empty($params['search'])) {
                $where_conditions[] = "(name LIKE :search OR email LIKE :search OR subject LIKE :search OR message LIKE :search)";
                $bind_params[':search'] = '%' . $params['search'] . '%';
            }
            
            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            
            // Count total records
            $count_query = "SELECT COUNT(*) as total FROM contacts {$where_clause}";
            $count_stmt = $this->db->prepare($count_query);
            foreach ($bind_params as $key => $value) {
                $count_stmt->bindValue($key, $value);
            }
            $count_stmt->execute();
            $total_records = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // Get contacts
            $query = "SELECT id, name, email, phone, company, subject, message, service_interest, 
                            status, priority, created_at, updated_at
                     FROM contacts 
                     {$where_clause}
                     ORDER BY created_at DESC 
                     LIMIT :limit OFFSET :offset";
            
            $stmt = $this->db->prepare($query);
            foreach ($bind_params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            
            return [
                'success' => true,
                'data' => $stmt->fetchAll(PDO::FETCH_ASSOC),
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => ceil($total_records / $limit),
                    'total_records' => $total_records,
                    'per_page' => $limit
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch contacts: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get single contact
     */
    public function getContact($id) {
        try {
            $query = "SELECT * FROM contacts WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                return [
                    'success' => true,
                    'data' => $stmt->fetch(PDO::FETCH_ASSOC)
                ];
            }
            
            return ['success' => false, 'message' => 'Contact not found'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch contact: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update contact status
     */
    public function updateContactStatus($id, $status, $assigned_to = null) {
        try {
            $query = "UPDATE contacts SET status = :status";
            $bind_params = [':id' => $id, ':status' => $status];
            
            if ($assigned_to !== null) {
                $query .= ", assigned_to = :assigned_to";
                $bind_params[':assigned_to'] = $assigned_to;
            }
            
            if ($status === 'replied') {
                $query .= ", replied_at = NOW()";
            }
            
            $query .= " WHERE id = :id";
            
            $stmt = $this->db->prepare($query);
            foreach ($bind_params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'Contact status updated successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to update contact status'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Contact update failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Delete contact
     */
    public function deleteContact($id) {
        try {
            $query = "DELETE FROM contacts WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            
            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'Contact deleted successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to delete contact'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Contact deletion failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Check rate limiting for contact submissions
     */
    public function checkRateLimit($ip_address, $time_window = 300, $max_submissions = 3) {
        try {
            $query = "SELECT COUNT(*) as count FROM contacts 
                     WHERE ip_address = :ip_address 
                     AND created_at > DATE_SUB(NOW(), INTERVAL :time_window SECOND)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->bindParam(':time_window', $time_window);
            $stmt->execute();
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] >= $max_submissions;
            
        } catch (Exception $e) {
            // If there's an error checking rate limit, allow the submission
            return false;
        }
    }
    
    /**
     * Send notification email for new contact
     */
    public function sendNotificationEmail($data) {
        // This would integrate with PHPMailer or similar
        // For now, just log the notification
        $message = "New contact form submission:\n";
        $message .= "Name: " . $data['name'] . "\n";
        $message .= "Email: " . $data['email'] . "\n";
        $message .= "Subject: " . $data['subject'] . "\n";
        $message .= "Message: " . $data['message'] . "\n";
        
        error_log("Contact notification: " . $message);
        
        // TODO: Implement actual email sending with PHPMailer
        return true;
    }
    
    /**
     * Get contact statistics for dashboard
     */
    public function getContactStats() {
        try {
            $stats = [];
            
            // Total contacts
            $query = "SELECT COUNT(*) as total FROM contacts";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // New contacts (last 7 days)
            $query = "SELECT COUNT(*) as new_contacts FROM contacts 
                     WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats['new_contacts'] = $stmt->fetch(PDO::FETCH_ASSOC)['new_contacts'];
            
            // Unread contacts
            $query = "SELECT COUNT(*) as unread FROM contacts WHERE status = 'new'";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats['unread'] = $stmt->fetch(PDO::FETCH_ASSOC)['unread'];
            
            // Contacts by service interest
            $query = "SELECT service_interest, COUNT(*) as count 
                     FROM contacts 
                     GROUP BY service_interest 
                     ORDER BY count DESC";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats['by_service'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'data' => $stats
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch contact stats: ' . $e->getMessage()];
        }
    }
}
?>
