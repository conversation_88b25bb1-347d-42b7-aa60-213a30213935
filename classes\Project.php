<?php
/**
 * Project Class
 * Handles project management operations
 */

class Project {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Get all projects with pagination and filtering
     */
    public function getProjects($params = []) {
        try {
            $page = isset($params['page']) ? (int)$params['page'] : 1;
            $limit = isset($params['limit']) ? (int)$params['limit'] : ITEMS_PER_PAGE;
            $offset = ($page - 1) * $limit;
            
            $where_conditions = ["status = 'active'"];
            $bind_params = [];
            
            // Filter by category
            if (!empty($params['category'])) {
                $where_conditions[] = "category = :category";
                $bind_params[':category'] = $params['category'];
            }
            
            // Filter by service type
            if (!empty($params['service_type'])) {
                $where_conditions[] = "service_type = :service_type";
                $bind_params[':service_type'] = $params['service_type'];
            }
            
            // Search functionality
            if (!empty($params['search'])) {
                $where_conditions[] = "(title LIKE :search OR description LIKE :search OR location LIKE :search)";
                $bind_params[':search'] = '%' . $params['search'] . '%';
            }
            
            $where_clause = implode(' AND ', $where_conditions);
            
            // Count total records
            $count_query = "SELECT COUNT(*) as total FROM projects WHERE {$where_clause}";
            $count_stmt = $this->db->prepare($count_query);
            foreach ($bind_params as $key => $value) {
                $count_stmt->bindValue($key, $value);
            }
            $count_stmt->execute();
            $total_records = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // Get projects
            $query = "SELECT id, title, slug, short_description, category, service_type, location, 
                            client_name, start_date, end_date, featured_image, gallery_images, 
                            created_at, updated_at
                     FROM projects 
                     WHERE {$where_clause}
                     ORDER BY sort_order ASC, created_at DESC 
                     LIMIT :limit OFFSET :offset";
            
            $stmt = $this->db->prepare($query);
            foreach ($bind_params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            
            $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Process gallery images
            foreach ($projects as &$project) {
                $project['gallery_images'] = json_decode($project['gallery_images'], true) ?: [];
            }
            
            return [
                'success' => true,
                'data' => $projects,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => ceil($total_records / $limit),
                    'total_records' => $total_records,
                    'per_page' => $limit
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch projects: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get single project by ID or slug
     */
    public function getProject($identifier, $by_slug = false) {
        try {
            $field = $by_slug ? 'slug' : 'id';
            
            $query = "SELECT * FROM projects WHERE {$field} = :identifier AND status = 'active'";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':identifier', $identifier);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $project = $stmt->fetch(PDO::FETCH_ASSOC);
                $project['gallery_images'] = json_decode($project['gallery_images'], true) ?: [];
                
                return [
                    'success' => true,
                    'data' => $project
                ];
            }
            
            return ['success' => false, 'message' => 'Project not found'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch project: ' . $e->getMessage()];
        }
    }
    
    /**
     * Create new project
     */
    public function createProject($data, $created_by) {
        try {
            // Validate required fields
            $required_fields = ['title', 'service_type'];
            foreach ($required_fields as $field) {
                if (empty($data[$field])) {
                    return ['success' => false, 'message' => "Field {$field} is required"];
                }
            }
            
            // Generate slug
            $slug = $this->generateUniqueSlug($data['title']);
            
            // Prepare gallery images
            $gallery_images = isset($data['gallery_images']) ? json_encode($data['gallery_images']) : null;
            
            $query = "INSERT INTO projects (title, slug, description, short_description, category, 
                                         service_type, location, client_name, start_date, end_date, 
                                         project_value, featured_image, gallery_images, meta_title, 
                                         meta_description, sort_order, created_by) 
                     VALUES (:title, :slug, :description, :short_description, :category, 
                            :service_type, :location, :client_name, :start_date, :end_date, 
                            :project_value, :featured_image, :gallery_images, :meta_title, 
                            :meta_description, :sort_order, :created_by)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':title', $data['title']);
            $stmt->bindParam(':slug', $slug);
            $stmt->bindParam(':description', $data['description'] ?? null);
            $stmt->bindParam(':short_description', $data['short_description'] ?? null);
            $stmt->bindParam(':category', $data['category'] ?? 'completed');
            $stmt->bindParam(':service_type', $data['service_type']);
            $stmt->bindParam(':location', $data['location'] ?? null);
            $stmt->bindParam(':client_name', $data['client_name'] ?? null);
            $stmt->bindParam(':start_date', $data['start_date'] ?? null);
            $stmt->bindParam(':end_date', $data['end_date'] ?? null);
            $stmt->bindParam(':project_value', $data['project_value'] ?? null);
            $stmt->bindParam(':featured_image', $data['featured_image'] ?? null);
            $stmt->bindParam(':gallery_images', $gallery_images);
            $stmt->bindParam(':meta_title', $data['meta_title'] ?? $data['title']);
            $stmt->bindParam(':meta_description', $data['meta_description'] ?? $data['short_description']);
            $stmt->bindParam(':sort_order', $data['sort_order'] ?? 0);
            $stmt->bindParam(':created_by', $created_by);
            
            if ($stmt->execute()) {
                $project_id = $this->db->lastInsertId();
                return [
                    'success' => true,
                    'message' => 'Project created successfully',
                    'project_id' => $project_id
                ];
            }
            
            return ['success' => false, 'message' => 'Failed to create project'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Project creation failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update project
     */
    public function updateProject($id, $data, $updated_by) {
        try {
            // Check if project exists
            $existing = $this->getProject($id);
            if (!$existing['success']) {
                return ['success' => false, 'message' => 'Project not found'];
            }
            
            // Generate new slug if title changed
            if (isset($data['title']) && $data['title'] !== $existing['data']['title']) {
                $data['slug'] = $this->generateUniqueSlug($data['title'], $id);
            }
            
            // Prepare gallery images
            if (isset($data['gallery_images'])) {
                $data['gallery_images'] = json_encode($data['gallery_images']);
            }
            
            // Build update query dynamically
            $update_fields = [];
            $bind_params = [':id' => $id];
            
            $allowed_fields = ['title', 'slug', 'description', 'short_description', 'category', 
                             'service_type', 'location', 'client_name', 'start_date', 'end_date', 
                             'project_value', 'featured_image', 'gallery_images', 'meta_title', 
                             'meta_description', 'sort_order', 'status'];
            
            foreach ($allowed_fields as $field) {
                if (isset($data[$field])) {
                    $update_fields[] = "{$field} = :{$field}";
                    $bind_params[":{$field}"] = $data[$field];
                }
            }
            
            if (empty($update_fields)) {
                return ['success' => false, 'message' => 'No fields to update'];
            }
            
            $query = "UPDATE projects SET " . implode(', ', $update_fields) . " WHERE id = :id";
            $stmt = $this->db->prepare($query);
            
            foreach ($bind_params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'Project updated successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to update project'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Project update failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Delete project
     */
    public function deleteProject($id) {
        try {
            $query = "UPDATE projects SET status = 'inactive' WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            
            if ($stmt->execute()) {
                return ['success' => true, 'message' => 'Project deleted successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to delete project'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Project deletion failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Generate unique slug
     */
    private function generateUniqueSlug($title, $exclude_id = null) {
        $slug = createSlug($title);
        $original_slug = $slug;
        $counter = 1;
        
        while ($this->slugExists($slug, $exclude_id)) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * Check if slug exists
     */
    private function slugExists($slug, $exclude_id = null) {
        $query = "SELECT id FROM projects WHERE slug = :slug";
        if ($exclude_id) {
            $query .= " AND id != :exclude_id";
        }
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':slug', $slug);
        if ($exclude_id) {
            $stmt->bindParam(':exclude_id', $exclude_id);
        }
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Get featured projects
     */
    public function getFeaturedProjects($limit = 6) {
        try {
            $query = "SELECT id, title, slug, short_description, category, service_type, 
                            location, featured_image, created_at
                     FROM projects 
                     WHERE status = 'active' 
                     ORDER BY sort_order ASC, created_at DESC 
                     LIMIT :limit";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            return [
                'success' => true,
                'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch featured projects: ' . $e->getMessage()];
        }
    }
}
?>
