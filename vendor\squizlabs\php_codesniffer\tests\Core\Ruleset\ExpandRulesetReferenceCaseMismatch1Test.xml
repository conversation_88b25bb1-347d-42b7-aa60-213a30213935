<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="ExpandRulesetReferenceTest" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

    <!-- Error handling: Case mismatch, will only error on case sensitive OSes.
         Correct case would be: PSR12.Functions.NullableTypeDeclaration,
         i.e. matching the case of the standard, category and sniff class name exactly. -->
    <rule ref="psr12.functions.nullabletypedeclaration"/>

</ruleset>
