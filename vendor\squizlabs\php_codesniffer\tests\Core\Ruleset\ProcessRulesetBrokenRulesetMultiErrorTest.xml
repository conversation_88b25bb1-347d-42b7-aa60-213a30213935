<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="ProcessRulesetBrokenRulesetTest" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

    <rule ref="PSR12.Operators">
        <properties>
            <property name="setforallincategory" value="true" />
            <property name="ignoreSpacingBeforeAssignments" value="false">
    </rule>

</ruleset>
