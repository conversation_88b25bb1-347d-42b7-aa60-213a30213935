<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="GetIncludePatternsTest" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

    <rule ref="PSR1"/>

    <rule ref="PSR1.Classes.ClassDeclaration">
        <include-pattern type="absolute">./src/*/file.php</include-pattern>
        <include-pattern type="relative">./bin/</include-pattern>
    </rule>

    <rule ref="Generic.Formatting.SpaceAfterCast">
        <include-pattern>./src/*/test\.php$</include-pattern>
    </rule>

</ruleset>
