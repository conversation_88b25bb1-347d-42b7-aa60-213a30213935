<?php
// Minimal test to identify the issue
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "PHP Version: " . phpversion() . "<br>";
echo "Current directory: " . __DIR__ . "<br>";
echo "Document root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";

// Test if basic PHP works
echo "Basic PHP is working!<br>";

// Test file existence
$files_to_check = [
    'config/config.php',
    'config/database.php',
    'classes/Auth.php',
    'vendor/autoload.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✓ {$file} exists<br>";
    } else {
        echo "✗ {$file} missing<br>";
    }
}

// Test config loading
echo "<h3>Testing config loading:</h3>";
try {
    require_once 'config/config.php';
    echo "✓ Config loaded successfully<br>";
    echo "Site name: " . SITE_NAME . "<br>";
} catch (Exception $e) {
    echo "✗ Config failed: " . $e->getMessage() . "<br>";
}

echo "<h3>Testing class loading:</h3>";
try {
    require_once 'classes/Auth.php';
    echo "✓ Auth class loaded<br>";
    
    $auth = new Auth();
    echo "✓ Auth class instantiated<br>";
} catch (Exception $e) {
    echo "✗ Auth class failed: " . $e->getMessage() . "<br>";
}

echo "<h3>All tests completed</h3>";
?>
