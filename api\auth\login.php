<?php
/**
 * User Login API Endpoint
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../classes/Auth.php';

setCORSHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['error' => 'Method not allowed'], 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }
    
    // Validate required fields
    if (empty($input['username']) || empty($input['password'])) {
        jsonResponse(['error' => 'Username and password are required'], 400);
    }
    
    // Sanitize input
    $username = sanitizeInput($input['username']);
    $password = $input['password']; // Don't sanitize password
    
    // Attempt login
    $auth = new Auth();
    $result = $auth->login($username, $password);
    
    if ($result['success']) {
        // Log successful login
        error_log("Successful login for user: " . $username);
        
        jsonResponse([
            'success' => true,
            'message' => 'Login successful',
            'token' => $result['token'],
            'user' => $result['user']
        ], 200);
    } else {
        // Log failed login attempt
        error_log("Failed login attempt for user: " . $username . " - " . $result['message']);
        
        jsonResponse([
            'success' => false,
            'error' => $result['message']
        ], 401);
    }
    
} catch (Exception $e) {
    error_log("Login error: " . $e->getMessage());
    jsonResponse(['error' => 'Internal server error'], 500);
}
?>
