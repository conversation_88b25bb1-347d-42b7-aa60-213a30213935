<?php
/**
 * .htaccess Diagnostic Tool
 * Test different .htaccess configurations
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>.htaccess Diagnostic Tool</h1>";

// Check if .htaccess exists
if (file_exists('.htaccess')) {
    echo "<h2>Current .htaccess Status</h2>";
    echo "<span style='color: green;'>✓ .htaccess file exists</span><br>";
    
    $htaccess_content = file_get_contents('.htaccess');
    $lines = count(explode("\n", $htaccess_content));
    echo "File size: " . strlen($htaccess_content) . " bytes<br>";
    echo "Lines: " . $lines . "<br>";
    
    echo "<h3>Current .htaccess Content:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($htaccess_content);
    echo "</pre>";
} else {
    echo "<span style='color: red;'>✗ .htaccess file not found</span><br>";
}

// Test different configurations
echo "<h2>Available .htaccess Configurations</h2>";

$configs = [
    'minimal' => '.htaccess_minimal',
    'safe' => '.htaccess_safe', 
    'working' => '.htaccess_working'
];

foreach ($configs as $name => $file) {
    if (file_exists($file)) {
        echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; background: #f9f9f9;'>";
        echo "<h4>{$name} Configuration</h4>";
        echo "<p>File: {$file}</p>";
        echo "<a href='?switch={$name}' style='background: #007cba; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Switch to {$name}</a>";
        echo "</div>";
    }
}

// Handle configuration switching
if (isset($_GET['switch'])) {
    $switch_to = $_GET['switch'];
    if (isset($configs[$switch_to]) && file_exists($configs[$switch_to])) {
        // Backup current .htaccess
        if (file_exists('.htaccess')) {
            copy('.htaccess', '.htaccess_backup_' . date('Y-m-d_H-i-s'));
        }
        
        // Copy new configuration
        if (copy($configs[$switch_to], '.htaccess')) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<strong>Success!</strong> Switched to {$switch_to} configuration.";
            echo "</div>";
            echo "<script>setTimeout(function(){ window.location.href = 'htaccess_test.php'; }, 2000);</script>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<strong>Error!</strong> Failed to switch configuration.";
            echo "</div>";
        }
    }
}

// Test website functionality
echo "<h2>Website Functionality Test</h2>";

$test_urls = [
    'Homepage' => 'index.php',
    'Simple Homepage' => 'index_simple.php',
    'Admin Login' => 'admin/login.php',
    'Contact Page' => 'contact.php',
    'Test Page' => 'test.php'
];

foreach ($test_urls as $name => $url) {
    $full_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/" . $url;
    
    echo "<div style='margin: 5px 0;'>";
    echo "<strong>{$name}:</strong> ";
    echo "<a href='{$full_url}' target='_blank'>{$url}</a> ";
    
    // Test URL accessibility
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $headers = @get_headers($full_url, 1, $context);
    if ($headers && strpos($headers[0], '200') !== false) {
        echo "<span style='color: green;'>✓ Working</span>";
    } elseif ($headers && strpos($headers[0], '500') !== false) {
        echo "<span style='color: red;'>✗ Internal Server Error</span>";
    } elseif ($headers && strpos($headers[0], '404') !== false) {
        echo "<span style='color: orange;'>⚠ Not Found</span>";
    } else {
        echo "<span style='color: gray;'>? Unknown</span>";
    }
    echo "</div>";
}

// Apache module information
echo "<h2>Apache Module Information</h2>";

if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $important_modules = ['mod_rewrite', 'mod_headers', 'mod_deflate', 'mod_expires'];
    
    foreach ($important_modules as $module) {
        if (in_array($module, $modules)) {
            echo "<span style='color: green;'>✓ {$module} is loaded</span><br>";
        } else {
            echo "<span style='color: red;'>✗ {$module} is not loaded</span><br>";
        }
    }
} else {
    echo "<span style='color: orange;'>⚠ Cannot check Apache modules (not running under Apache or function not available)</span><br>";
}

// Quick fixes
echo "<h2>Quick Fixes</h2>";
echo "<div style='background: #e7f3ff; border: 1px solid #b8daff; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
echo "<h4>If you're getting Internal Server Error (500):</h4>";
echo "<ol>";
echo "<li><a href='?switch=safe'>Switch to Safe Configuration</a> (no rewrite rules)</li>";
echo "<li><a href='?switch=minimal'>Switch to Minimal Configuration</a> (basic rewrite rules)</li>";
echo "<li><a href='?switch=working'>Switch to Working Configuration</a> (full features)</li>";
echo "<li>Check Apache error logs in XAMPP Control Panel</li>";
echo "<li>Ensure mod_rewrite is enabled in Apache</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><small>Diagnostic completed at " . date('Y-m-d H:i:s') . "</small></p>";
?>
