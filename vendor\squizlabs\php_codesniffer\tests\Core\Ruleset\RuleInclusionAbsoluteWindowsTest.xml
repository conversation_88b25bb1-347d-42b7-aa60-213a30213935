<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="RuleInclusionAbsoluteWindowsTest" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

    <!-- %path_slash_back% will be replaced on the fly -->
    <rule ref="%path_slash_back%\src\Standards\Generic\Sniffs\Formatting\SpaceAfterCastSniff.php">
        <properties>
            <property name="spacing" value="10" />
        </properties>
    </rule>

</ruleset>
