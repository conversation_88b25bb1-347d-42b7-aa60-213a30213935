<?php
/**
 * Database Setup Script
 * Run this file to create the database and tables
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Flori Construction - Database Setup</h1>";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'flori_construction';

try {
    // Connect to MySQL server (without selecting database)
    $pdo = new PDO("mysql:host={$host}", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Step 1: Creating Database</h2>";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS {$database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<span style='color: green;'>✓ Database '{$database}' created successfully</span><br>";
    
    // Select the database
    $pdo->exec("USE {$database}");
    
    echo "<h2>Step 2: Creating Tables</h2>";
    
    // Read and execute schema file
    $schema_file = 'database/schema.sql';
    if (file_exists($schema_file)) {
        $schema_sql = file_get_contents($schema_file);
        
        // Remove the CREATE DATABASE and USE statements since we already did that
        $schema_sql = preg_replace('/CREATE DATABASE.*?;/i', '', $schema_sql);
        $schema_sql = preg_replace('/USE.*?;/i', '', $schema_sql);
        
        // Split into individual statements
        $statements = array_filter(array_map('trim', explode(';', $schema_sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement);
                    
                    // Extract table name for display
                    if (preg_match('/CREATE TABLE\s+(\w+)/i', $statement, $matches)) {
                        echo "<span style='color: green;'>✓ Table '{$matches[1]}' created</span><br>";
                    }
                } catch (PDOException $e) {
                    echo "<span style='color: orange;'>⚠ Statement skipped (might already exist): " . substr($statement, 0, 50) . "...</span><br>";
                }
            }
        }
    } else {
        echo "<span style='color: red;'>✗ Schema file not found: {$schema_file}</span><br>";
    }
    
    echo "<h2>Step 3: Inserting Sample Data</h2>";
    
    // Read and execute seed data file
    $seed_file = 'database/seed_data.sql';
    if (file_exists($seed_file)) {
        $seed_sql = file_get_contents($seed_file);
        
        // Remove the USE statement
        $seed_sql = preg_replace('/USE.*?;/i', '', $seed_sql);
        
        // Split into individual statements
        $statements = array_filter(array_map('trim', explode(';', $seed_sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && stripos($statement, 'INSERT') !== false) {
                try {
                    $pdo->exec($statement);
                    
                    // Extract table name for display
                    if (preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches)) {
                        echo "<span style='color: green;'>✓ Sample data inserted into '{$matches[1]}'</span><br>";
                    }
                } catch (PDOException $e) {
                    echo "<span style='color: orange;'>⚠ Insert skipped (might already exist): " . $e->getMessage() . "</span><br>";
                }
            }
        }
    } else {
        echo "<span style='color: red;'>✗ Seed data file not found: {$seed_file}</span><br>";
    }
    
    echo "<h2>Step 4: Verification</h2>";
    
    // Verify tables were created
    $tables = ['users', 'projects', 'services', 'contacts', 'media', 'settings', 'testimonials', 'activity_logs'];
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE '{$table}'");
        $stmt->execute();
        if ($stmt->rowCount() > 0) {
            // Count records in table
            $count_stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$table}");
            $count_stmt->execute();
            $count = $count_stmt->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<span style='color: green;'>✓ Table '{$table}' exists with {$count} records</span><br>";
        } else {
            echo "<span style='color: red;'>✗ Table '{$table}' not found</span><br>";
        }
    }
    
    echo "<h2>Setup Complete!</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Default Admin Credentials:</h3>";
    echo "<strong>Username:</strong> admin<br>";
    echo "<strong>Password:</strong> admin123<br>";
    echo "<strong>Email:</strong> <EMAIL><br>";
    echo "</div>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ul>";
    echo "<li><a href='test.php'>Run System Test</a> - Check if everything is working</li>";
    echo "<li><a href='index_simple.php'>View Simple Homepage</a> - Basic version without database</li>";
    echo "<li><a href='index.php'>View Full Homepage</a> - Complete version with database</li>";
    echo "<li><a href='admin/login.php'>Access Admin Panel</a> - Login with admin credentials</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h2>Database Error</h2>";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<strong>Error:</strong> " . $e->getMessage() . "<br>";
    echo "<strong>Code:</strong> " . $e->getCode() . "<br>";
    echo "</div>";
    
    echo "<h3>Common Solutions:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/MySQL is running</li>";
    echo "<li>Check database credentials in config/database.php</li>";
    echo "<li>Ensure MySQL user has CREATE DATABASE privileges</li>";
    echo "<li>Try accessing phpMyAdmin to test database connection</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><small>Setup script completed at " . date('Y-m-d H:i:s') . "</small></p>";
?>
